<?php
/**
 * Title: Footer
 * Slug: kyriakis/footer
 * Categories: kyriakis-patterns
 * Description: Modern footer with contact information and social links
 */
?>

<!-- wp:html -->
<footer class="kyriakis-footer">
  <div class="kyriakis-container">
    <div class="kyriakis-footer-content">
      <div class="kyriakis-footer-main">
        <div class="kyriakis-footer-brand">
          <div class="kyriakis-footer-logo">
            <div class="kyriakis-logo-icon">
              <div class="kyriakis-logo-main">
                <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/>
                </svg>
              </div>
            </div>
            <div class="kyriakis-footer-brand-text">
              <h3>Kyriakis Plumber</h3>
              <p id="footer-tagline">Premium Plumbing Services</p>
            </div>
          </div>
          <p id="footer-description">Professional hydraulic solutions with modern techniques and premium materials. Serving Greece with guaranteed quality work since 2014.</p>
        </div>
        
        <div class="kyriakis-footer-contact">
          <h4 id="footer-contact-title">Contact Information</h4>
          <div class="kyriakis-footer-contact-items">
            <div class="kyriakis-footer-contact-item">
              <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
              </svg>
              <div>
                <span class="kyriakis-footer-contact-label" id="footer-phone-label">Phone</span>
                <a href="tel:+306985814213" class="kyriakis-footer-contact-value">+30 ************</a>
              </div>
            </div>
            
            <div class="kyriakis-footer-contact-item">
              <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                <polyline points="22,6 12,13 2,6"/>
              </svg>
              <div>
                <span class="kyriakis-footer-contact-label" id="footer-email-label">Email</span>
                <a href="mailto:<EMAIL>" class="kyriakis-footer-contact-value"><EMAIL></a>
              </div>
            </div>
            
            <div class="kyriakis-footer-contact-item">
              <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                <circle cx="12" cy="10" r="3"/>
              </svg>
              <div>
                <span class="kyriakis-footer-contact-label" id="footer-location-label">Location</span>
                <span class="kyriakis-footer-contact-value" id="footer-location-value">Greece</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="kyriakis-footer-services">
          <h4 id="footer-services-title">Our Services</h4>
          <ul class="kyriakis-footer-services-list">
            <li><a href="#services" onclick="scrollToSection('services')" id="footer-service-1">Pipe Installation</a></li>
            <li><a href="#services" onclick="scrollToSection('services')" id="footer-service-2">Leak Repairs</a></li>
            <li><a href="#services" onclick="scrollToSection('services')" id="footer-service-3">Heating Systems</a></li>
            <li><a href="#services" onclick="scrollToSection('services')" id="footer-service-4">Solar Systems</a></li>
            <li><a href="#services" onclick="scrollToSection('services')" id="footer-service-5">Emergency Service</a></li>
            <li><a href="#services" onclick="scrollToSection('services')" id="footer-service-6">Boiler Installation</a></li>
          </ul>
        </div>
        
        <div class="kyriakis-footer-cta">
          <h4 id="footer-cta-title">Need Help?</h4>
          <p id="footer-cta-text">Contact us for professional plumbing services</p>
          <div class="kyriakis-footer-cta-buttons">
            <a href="tel:+306985814213" class="kyriakis-footer-btn kyriakis-footer-btn-primary">
              <svg class="kyriakis-icon-sm" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
              </svg>
              <span id="footer-call-btn">Call Now</span>
            </a>
            <a href="https://wa.me/306985814213" target="_blank" class="kyriakis-footer-btn kyriakis-footer-btn-whatsapp">
              <svg class="kyriakis-icon-sm" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"/>
              </svg>
              <span>WhatsApp</span>
            </a>
          </div>
        </div>
      </div>
      
      <div class="kyriakis-footer-bottom">
        <div class="kyriakis-footer-bottom-content">
          <p id="footer-copyright">&copy; 2025 Kyriakis Plumber. All rights reserved.</p>
          <div class="kyriakis-footer-links">
            <a href="#" id="footer-privacy">Privacy Policy</a>
            <a href="#" id="footer-terms">Terms of Service</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</footer>

<style>
.kyriakis-footer {
  background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 100%);
  color: white;
  padding: 4rem 0 0;
  position: relative;
  overflow: hidden;
}

.kyriakis-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.kyriakis-footer-main {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

@media (max-width: 1024px) {
  .kyriakis-footer-main {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .kyriakis-footer-main {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
}

.kyriakis-footer-brand {
  max-width: 400px;
}

.kyriakis-footer-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.kyriakis-footer-brand-text h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.kyriakis-footer-brand-text p {
  margin: 0;
  font-size: 0.875rem;
  color: #60a5fa;
  font-weight: 600;
}

.kyriakis-footer-brand > p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-top: 1rem;
}

.kyriakis-footer h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: white;
}

.kyriakis-footer-contact-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.kyriakis-footer-contact-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.kyriakis-footer-contact-item svg {
  color: #60a5fa;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.kyriakis-footer-contact-label {
  display: block;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.25rem;
}

.kyriakis-footer-contact-value {
  color: white;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.kyriakis-footer-contact-value:hover {
  color: #60a5fa;
  text-decoration: none;
}

.kyriakis-footer-services-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.kyriakis-footer-services-list li {
  margin-bottom: 0.75rem;
}

.kyriakis-footer-services-list a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 500;
}

.kyriakis-footer-services-list a:hover {
  color: #60a5fa;
  text-decoration: none;
}

.kyriakis-footer-cta p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1.5rem;
}

.kyriakis-footer-cta-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.kyriakis-footer-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.kyriakis-footer-btn-primary {
  background: linear-gradient(45deg, #2563eb, #9333ea);
  color: white;
}

.kyriakis-footer-btn-primary:hover {
  background: linear-gradient(45deg, #1d4ed8, #7c3aed);
  transform: translateY(-2px);
  color: white;
  text-decoration: none;
}

.kyriakis-footer-btn-whatsapp {
  background: linear-gradient(45deg, #10b981, #059669);
  color: white;
}

.kyriakis-footer-btn-whatsapp:hover {
  background: linear-gradient(45deg, #059669, #047857);
  transform: translateY(-2px);
  color: white;
  text-decoration: none;
}

.kyriakis-footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 2rem 0;
}

.kyriakis-footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@media (max-width: 768px) {
  .kyriakis-footer-bottom-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

.kyriakis-footer-bottom p {
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}

.kyriakis-footer-links {
  display: flex;
  gap: 2rem;
}

.kyriakis-footer-links a {
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

.kyriakis-footer-links a:hover {
  color: #60a5fa;
  text-decoration: none;
}

/* Icons */
.kyriakis-icon {
  width: 1.75rem;
  height: 1.75rem;
  fill: currentColor;
}

.kyriakis-icon-sm {
  width: 1rem;
  height: 1rem;
  fill: currentColor;
}

.kyriakis-icon-md {
  width: 1.25rem;
  height: 1.25rem;
  fill: currentColor;
}
</style>

<script>
// Footer language translations
const footerTranslations = {
  gr: {
    'footer-tagline': 'Premium Υδραυλικές Υπηρεσίες',
    'footer-description': 'Επαγγελματικές υδραυλικές λύσεις με σύγχρονες τεχνικές και premium υλικά. Εξυπηρετούμε την Ελλάδα με εγγυημένη ποιότητα εργασίας από το 2014.',
    'footer-contact-title': 'Στοιχεία Επικοινωνίας',
    'footer-phone-label': 'Τηλέφωνο',
    'footer-email-label': 'Email',
    'footer-location-label': 'Τοποθεσία',
    'footer-location-value': 'Ελλάδα',
    'footer-services-title': 'Οι Υπηρεσίες μας',
    'footer-service-1': 'Εγκατάσταση Σωληνώσεων',
    'footer-service-2': 'Επισκευή Διαρροών',
    'footer-service-3': 'Συστήματα Θέρμανσης',
    'footer-service-4': 'Ηλιακά Συστήματα',
    'footer-service-5': 'Υπηρεσία Έκτακτης Ανάγκης',
    'footer-service-6': 'Εγκατάσταση Λεβήτων',
    'footer-cta-title': 'Χρειάζεστε Βοήθεια;',
    'footer-cta-text': 'Επικοινωνήστε μαζί μας για επαγγελματικές υδραυλικές υπηρεσίες',
    'footer-call-btn': 'Κλήση Τώρα',
    'footer-copyright': '© 2025 Kyriakis Plumber. Όλα τα δικαιώματα διατηρούνται.',
    'footer-privacy': 'Πολιτική Απορρήτου',
    'footer-terms': 'Όροι Χρήσης'
  },
  en: {
    'footer-tagline': 'Premium Plumbing Services',
    'footer-description': 'Professional hydraulic solutions with modern techniques and premium materials. Serving Greece with guaranteed quality work since 2014.',
    'footer-contact-title': 'Contact Information',
    'footer-phone-label': 'Phone',
    'footer-email-label': 'Email',
    'footer-location-label': 'Location',
    'footer-location-value': 'Greece',
    'footer-services-title': 'Our Services',
    'footer-service-1': 'Pipe Installation',
    'footer-service-2': 'Leak Repairs',
    'footer-service-3': 'Heating Systems',
    'footer-service-4': 'Solar Systems',
    'footer-service-5': 'Emergency Service',
    'footer-service-6': 'Boiler Installation',
    'footer-cta-title': 'Need Help?',
    'footer-cta-text': 'Contact us for professional plumbing services',
    'footer-call-btn': 'Call Now',
    'footer-copyright': '© 2025 Kyriakis Plumber. All rights reserved.',
    'footer-privacy': 'Privacy Policy',
    'footer-terms': 'Terms of Service'
  }
};

// Listen for language change events
window.addEventListener('languageChanged', function(e) {
  const lang = e.detail.language;
  Object.keys(footerTranslations[lang]).forEach(key => {
    const element = document.getElementById(key);
    if (element) {
      element.textContent = footerTranslations[lang][key];
    }
  });
});
</script>
<!-- /wp:html -->
