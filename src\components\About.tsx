import React, { useState, useEffect } from 'react';
import { Award, Users, Clock, Shield, Star, CheckCircle, Zap, Trophy, Target } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const About: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [counters, setCounters] = useState({ clients: 0, years: 0, projects: 0, rating: 0 });
  const { t } = useLanguage();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Animate counters
          const animateCounter = (key: keyof typeof counters, target: number, duration: number) => {
            let start = 0;
            const increment = target / (duration / 16);
            const timer = setInterval(() => {
              start += increment;
              if (start >= target) {
                start = target;
                clearInterval(timer);
              }
              setCounters(prev => ({ ...prev, [key]: Math.floor(start) }));
            }, 16);
          };

          setTimeout(() => {
            animateCounter('clients', 1000, 2000);
            animateCounter('years', 30, 1500);
            animateCounter('projects', 500, 2500);
            animateCounter('rating', 5, 1000);
          }, 500);
        }
      },
      { threshold: 0.3 }
    );

    const element = document.getElementById('about');
    if (element) observer.observe(element);

    return () => observer.disconnect();
  }, []);

  const stats = [
    { 
      icon: <Users className="w-8 h-8" />, 
      number: `${counters.clients}+`, 
      label: t('about.clients'),
      color: 'from-blue-500 to-cyan-500'
    },
    { 
      icon: <Clock className="w-8 h-8" />, 
      number: `${counters.years}+`, 
      label: t('about.years'),
      color: 'from-green-500 to-emerald-500'
    },
    { 
      icon: <Award className="w-8 h-8" />, 
      number: `${counters.projects}+`, 
      label: t('about.projects'),
      color: 'from-purple-500 to-pink-500'
    },
    { 
      icon: <Star className="w-8 h-8" />, 
      number: `${counters.rating}★`, 
      label: t('about.rating'),
      color: 'from-yellow-500 to-orange-500'
    }
  ];

  const features = [
    { text: 'Πιστοποιημένοι τεχνικοί με πολυετή εμπειρία', icon: Trophy },
    { text: 'Χρήση σύγχρονων υλικών και τεχνολογιών 2025', icon: Zap },
    { text: 'Εγγύηση ποιότητας σε όλες τις εργασίες', icon: Shield },
    { text: '24/7 υποστήριξη για επείγοντα περιστατικά', icon: Clock },
    { text: 'Δωρεάν εκτίμηση και προσφορές', icon: Target },
    { text: 'Ανταγωνιστικές τιμές χωρίς κρυφά κόστη', icon: CheckCircle }
  ];

  return (
    <section id="about" className="py-24 bg-gradient-to-br from-white to-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-20 items-center">
          {/* Content */}
          <div className={`space-y-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-10'}`}>
            <div>
              <div className="flex items-center gap-3 mb-6">
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-2xl">
                  <Shield className="w-6 h-6 text-white" />
                </div>
                <span className="text-blue-600 font-semibold text-lg">{t('about.company')}</span>
              </div>
              
              <h2 className="text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-8">
                {t('about.title1')}
                <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  {t('about.title2')}
                </span>
              </h2>
              
              <div className="space-y-6 text-lg text-gray-600 leading-relaxed">
                <p>
                  {t('about.description1').split('30 χρόνια εμπειρίας').map((part, index, array) => (
                    <React.Fragment key={index}>
                      {part}
                      {index < array.length - 1 && (
                        <span className="font-bold text-blue-600">30 χρόνια εμπειρίας</span>
                      )}
                    </React.Fragment>
                  ))}
                </p>
                <p>
                  {t('about.description2').split('εγγύηση ποιότητας').map((part, index, array) => (
                    <React.Fragment key={index}>
                      {part}
                      {index < array.length - 1 && (
                        <span className="font-bold text-purple-600">εγγύηση ποιότητας</span>
                      )}
                    </React.Fragment>
                  ))}
                </p>
              </div>
            </div>

            {/* Features */}
            <div className="space-y-4">
              {features.map((feature, index) => (
                <div 
                  key={index} 
                  className={`flex items-center gap-4 p-4 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border border-gray-100 ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'}`}
                  style={{ transitionDelay: `${index * 100}ms` }}
                >
                  <div className="bg-gradient-to-r from-blue-500 to-purple-500 p-3 rounded-xl">
                    <feature.icon className="w-5 h-5 text-white" />
                  </div>
                  <span className="text-gray-700 font-medium">{feature.text}</span>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-2xl font-bold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 hover:scale-105 shadow-xl">
                {t('about.quote.btn')}
              </button>
              <button className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-2xl font-bold hover:bg-blue-600 hover:text-white transition-all duration-300 hover:scale-105">
                {t('about.projects.btn')}
              </button>
            </div>
          </div>

          {/* Stats & Visual */}
          <div className={`space-y-8 transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-10'}`}>
            {/* Stats Grid */}
            <div className="grid grid-cols-2 gap-6">
              {stats.map((stat, index) => (
                <div key={index} className="group">
                  <div className="bg-white rounded-3xl p-8 text-center shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-gray-100">
                    <div className={`bg-gradient-to-r ${stat.color} p-4 rounded-2xl mb-4 mx-auto w-fit shadow-lg group-hover:shadow-xl transition-all duration-300`}>
                      {stat.icon}
                    </div>
                    <div className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
                      {stat.number}
                    </div>
                    <div className="text-sm text-gray-600 font-medium">
                      {stat.label}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Certification Badge */}
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-3xl p-8 text-white text-center shadow-2xl border border-white/20">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-3xl blur-xl"></div>
                <div className="relative">
                  <div className="bg-white/20 p-4 rounded-2xl w-fit mx-auto mb-6">
                    <Shield className="w-12 h-12 text-white" />
                  </div>
                  <h3 className="text-3xl font-bold mb-4">{t('about.certified')}</h3>
                  <p className="text-blue-100 mb-6 leading-relaxed">
                    {t('about.cert.description')}
                  </p>
                  <div className="flex flex-wrap justify-center gap-3 text-sm">
                    {['ISO 9001', 'Άδεια Εγκατάστασης', 'Ασφάλεια Εργασίας', 'Πιστοποίηση 2025'].map((cert, index) => (
                      <span key={index} className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full border border-white/30 hover:bg-white/30 transition-all duration-300">
                        {cert}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;