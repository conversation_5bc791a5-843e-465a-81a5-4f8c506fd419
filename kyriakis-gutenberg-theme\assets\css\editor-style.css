/**
 * Block Editor Styles
 * 
 * @package KyriakisPlumber
 * @since 1.0.0
 */

/* Editor wrapper styles */
.editor-styles-wrapper {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
}

/* Custom properties for editor */
.editor-styles-wrapper {
    --kyriakis-primary: #003b6f;
    --kyriakis-secondary: #2563eb;
    --kyriakis-accent: #9333ea;
    --kyriakis-success: #10b981;
    --kyriakis-warning: #f59e0b;
    --kyriakis-error: #ef4444;
    --kyriakis-white: #ffffff;
    --kyriakis-gray-50: #f9fafb;
    --kyriakis-gray-100: #f3f4f6;
    --kyriakis-gray-200: #e5e7eb;
    --kyriakis-gray-300: #d1d5db;
    --kyriakis-gray-400: #9ca3af;
    --kyriakis-gray-500: #6b7280;
    --kyriakis-gray-600: #4b5563;
    --kyriakis-gray-700: #374151;
    --kyriakis-gray-800: #1f2937;
    --kyriakis-gray-900: #111827;
}

/* Block editor specific styles */
.wp-block {
    max-width: none;
}

/* Custom HTML blocks styling in editor */
.wp-block[data-type="core/html"] {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    background: #f9fafb;
    position: relative;
}

.wp-block[data-type="core/html"]:hover {
    border-color: var(--kyriakis-secondary);
    background: #f3f4f6;
}

.wp-block[data-type="core/html"]::before {
    content: "Custom HTML Block";
    position: absolute;
    top: -10px;
    left: 10px;
    background: var(--kyriakis-secondary);
    color: white;
    padding: 2px 8px;
    font-size: 12px;
    border-radius: 4px;
    font-weight: 600;
}

/* Pattern blocks styling */
.wp-block[data-type="core/pattern"] {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 0;
    overflow: hidden;
    position: relative;
}

.wp-block[data-type="core/pattern"]:hover {
    border-color: var(--kyriakis-secondary);
}

.wp-block[data-type="core/pattern"]::before {
    content: "Kyriakis Pattern";
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--kyriakis-accent);
    color: white;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    font-weight: 600;
    z-index: 10;
}

/* Button block customizations */
.wp-block-button .wp-block-button__link {
    border-radius: 1rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.wp-block-button.is-style-kyriakis-primary .wp-block-button__link {
    background: linear-gradient(45deg, var(--kyriakis-secondary), var(--kyriakis-accent));
    border: none;
}

.wp-block-button.is-style-kyriakis-secondary .wp-block-button__link {
    background: transparent;
    border: 2px solid var(--kyriakis-secondary);
    color: var(--kyriakis-secondary);
}

.wp-block-button.is-style-kyriakis-success .wp-block-button__link {
    background: linear-gradient(45deg, var(--kyriakis-success), #059669);
    border: none;
}

/* Group block customizations */
.wp-block-group.is-style-kyriakis-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1rem;
}

.wp-block-group.is-style-kyriakis-gradient {
    background: linear-gradient(135deg, var(--kyriakis-primary), var(--kyriakis-secondary));
    color: white;
    border-radius: 1rem;
}

.wp-block-group.is-style-kyriakis-shadow {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border-radius: 1rem;
}

/* Heading customizations */
.wp-block-heading.is-style-kyriakis-gradient {
    background: linear-gradient(45deg, var(--kyriakis-secondary), var(--kyriakis-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Column block customizations */
.wp-block-columns.is-style-kyriakis-equal-height .wp-block-column {
    display: flex;
    flex-direction: column;
}

/* Image block customizations */
.wp-block-image.is-style-kyriakis-rounded img {
    border-radius: 1rem;
}

.wp-block-image.is-style-kyriakis-shadow img {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border-radius: 0.5rem;
}

/* Cover block customizations */
.wp-block-cover.is-style-kyriakis-gradient {
    background: linear-gradient(135deg, var(--kyriakis-primary), var(--kyriakis-secondary));
}

.wp-block-cover.is-style-kyriakis-glass::before {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

/* List block customizations */
.wp-block-list.is-style-kyriakis-checkmarks li::marker {
    content: "✓ ";
    color: var(--kyriakis-success);
    font-weight: bold;
}

/* Quote block customizations */
.wp-block-quote.is-style-kyriakis-modern {
    border-left: 4px solid var(--kyriakis-secondary);
    padding-left: 1.5rem;
    font-style: italic;
    background: var(--kyriakis-gray-50);
    border-radius: 0.5rem;
    padding: 1.5rem;
}

/* Separator customizations */
.wp-block-separator.is-style-kyriakis-gradient {
    background: linear-gradient(90deg, transparent, var(--kyriakis-secondary), transparent);
    height: 2px;
    border: none;
}

/* Table customizations */
.wp-block-table.is-style-kyriakis-modern table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.wp-block-table.is-style-kyriakis-modern th {
    background: var(--kyriakis-secondary);
    color: white;
    font-weight: 600;
}

.wp-block-table.is-style-kyriakis-modern tr:nth-child(even) {
    background: var(--kyriakis-gray-50);
}

/* Media & Text customizations */
.wp-block-media-text.is-style-kyriakis-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1rem;
    overflow: hidden;
}

/* Gallery customizations */
.wp-block-gallery.is-style-kyriakis-rounded .wp-block-image img {
    border-radius: 0.5rem;
}

/* Responsive design for editor */
@media (max-width: 768px) {
    .editor-styles-wrapper {
        font-size: 14px;
    }
    
    .wp-block[data-type="core/html"] {
        padding: 0.75rem;
        margin: 0.75rem 0;
    }
}

/* Dark mode support for editor */
@media (prefers-color-scheme: dark) {
    .wp-block[data-type="core/html"] {
        background: #1f2937;
        border-color: #374151;
        color: #e5e7eb;
    }
    
    .wp-block[data-type="core/html"]:hover {
        background: #111827;
        border-color: var(--kyriakis-secondary);
    }
}

/* Animation classes for editor preview */
.kyriakis-fade-in {
    animation: kyriakisFadeIn 0.6s ease-out;
}

.kyriakis-slide-up {
    animation: kyriakisSlideUp 0.6s ease-out;
}

.kyriakis-scale-in {
    animation: kyriakisScaleIn 0.6s ease-out;
}

@keyframes kyriakisFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes kyriakisSlideUp {
    from { 
        opacity: 0; 
        transform: translateY(30px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

@keyframes kyriakisScaleIn {
    from { 
        opacity: 0; 
        transform: scale(0.9); 
    }
    to { 
        opacity: 1; 
        transform: scale(1); 
    }
}

/* Utility classes for editor */
.kyriakis-text-gradient {
    background: linear-gradient(45deg, var(--kyriakis-secondary), var(--kyriakis-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.kyriakis-glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1rem;
}

.kyriakis-shadow-lg {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.kyriakis-rounded-lg {
    border-radius: 1rem;
}
