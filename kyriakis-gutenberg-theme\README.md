# Kyriakis Plumber - Full Gutenberg Theme

A modern, fully Gutenberg-compatible WordPress theme for Kyriakis Plumber services. Features bilingual support (Greek/English), modern design with glassmorphism effects, and complete block editor integration.

## 🚀 Features

### ✨ Full Site Editing (FSE)
- Complete Gutenberg block theme
- Custom block patterns for all sections
- Template parts for header and footer
- Full customization through WordPress editor

### 🌐 Bilingual Support
- Greek and English language switching
- Professional translations
- Dynamic content updates
- SEO-friendly language implementation

### 🎨 Modern Design
- Glassmorphism effects
- Gradient backgrounds
- Smooth animations
- Apple-level design aesthetics
- Mobile-first responsive design

### 📱 Mobile Optimized
- Touch-friendly navigation
- Responsive layouts
- Optimized for all devices
- Fast loading times

### 🔧 Developer Friendly
- Clean, semantic code
- Well-documented
- Extensible architecture
- WordPress coding standards

## 📋 Installation Instructions

### Step 1: Upload Theme
1. Download the `kyriakis-gutenberg-theme` folder
2. Compress it into a ZIP file
3. In WordPress Admin, go to **Appearance > Themes**
4. Click **Add New > Upload Theme**
5. Upload the ZIP file and activate the theme

### Step 2: Upload Images
Upload these images to your WordPress Media Library:
- `Λεβητοστάσιο.jpg`
- `Ηλιακ<PERSON> συστήματα.jpg`
- `Ενδοδαπεδια θερμανση.jpg`
- `Θερμοσίφωνο.jpg`
- `Επαγγελματική επισκευή υδραυλικών προβλημάτων με Εγγύηση ποιότητας.jpg`
- `Εγκατάσταση νέων σωληνώσεων με σύγχρονα υλικά και τεχνικές.jpg`
- `logo-valsir-en-gb.png`
- `logo-aquatherm.png`
- `uponor.jpg`
- `rahau-sunergasia.jpg.webp`

### Step 3: Create Your Homepage
1. Go to **Pages > Add New**
2. Create a new page titled "Home"
3. In the block editor, add the following patterns in order:
   - **Kyriakis Plumber > Header with Navigation**
   - **Kyriakis Plumber > Hero Section**
   - **Kyriakis Plumber > Services Showcase** (you'll need to create this)
   - **Kyriakis Plumber > About Section** (you'll need to create this)
   - **Kyriakis Plumber > Contact Form** (you'll need to create this)
   - **Kyriakis Plumber > Footer**

### Step 4: Set as Homepage
1. Go to **Settings > Reading**
2. Select "A static page" for homepage displays
3. Choose your "Home" page as the homepage

### Step 5: Customize Colors and Typography
1. Go to **Appearance > Editor > Styles**
2. Customize colors, typography, and spacing
3. The theme includes predefined color palette and gradients

## 🎨 Using Block Patterns

### Available Patterns
- **Header with Navigation** - Modern header with logo, menu, and language switcher
- **Hero Section** - Animated hero with call-to-action buttons
- **Footer** - Complete footer with contact information

### How to Use Patterns
1. In the block editor, click the **+** button
2. Go to the **Patterns** tab
3. Look for **Kyriakis Plumber** category
4. Click on any pattern to insert it

### Customizing Patterns
- Each pattern is fully editable in the block editor
- Change text, colors, and images directly
- Patterns maintain their responsive design

## 🌐 Language Switching

### How It Works
- Language switcher in header automatically translates content
- JavaScript handles dynamic content updates
- No plugins required for basic bilingual functionality

### Adding Translations
To add translations for new content:
1. Find the JavaScript section in the pattern
2. Add your translations to the translations object
3. Use the same key structure as existing translations

Example:
```javascript
const translations = {
  gr: {
    'your-element-id': 'Greek Text'
  },
  en: {
    'your-element-id': 'English Text'
  }
};
```

## 🔧 Customization

### Colors
The theme uses CSS custom properties defined in `theme.json`:
- `--kyriakis-primary`: #003b6f (Dark Blue)
- `--kyriakis-secondary`: #2563eb (Blue)
- `--kyriakis-accent`: #9333ea (Purple)
- `--kyriakis-success`: #10b981 (Green)

### Typography
- System font stack for optimal performance
- Responsive font sizes
- Custom font weights and line heights

### Spacing
- Consistent spacing scale
- Responsive margins and padding
- Block gap settings

## 📞 Contact Form Setup

### Option 1: Contact Form 7 (Recommended)
1. Install Contact Form 7 plugin
2. Create a contact form with these fields:
   - Name (text field)
   - Email (email field)
   - Phone (tel field)
   - Service (dropdown)
   - Message (textarea)
3. Use the shortcode in a Custom HTML block

### Option 2: EmailJS Integration
1. Sign up at [EmailJS](https://www.emailjs.com/)
2. Create a service and template
3. Create a contact form pattern with EmailJS integration
4. Update with your credentials

### Option 3: Simple Contact Information
- Use the existing patterns that include phone and email links
- Add WhatsApp integration (already included)
- Direct phone calls and emails

## 🚀 Performance Optimization

### Built-in Optimizations
- Minimal CSS and JavaScript
- Optimized animations
- Efficient image loading
- Clean HTML structure

### Recommended Plugins
- **Caching**: WP Rocket or W3 Total Cache
- **Image Optimization**: Smush or ShortPixel
- **SEO**: Yoast SEO or RankMath
- **Security**: Wordfence

## 🔍 SEO Features

### Built-in SEO
- Semantic HTML structure
- Proper heading hierarchy
- Alt text for images
- Meta descriptions support
- Schema markup ready

### Language SEO
- Proper `lang` attributes
- Hreflang support ready
- Multilingual sitemap compatible

## 📱 Mobile Features

### Touch Optimizations
- Large touch targets
- Swipe-friendly navigation
- Mobile-optimized forms
- Fast tap responses

### Performance
- Mobile-first CSS
- Optimized images
- Minimal JavaScript
- Fast loading times

## 🛠️ Development

### File Structure
```
kyriakis-gutenberg-theme/
├── style.css (Theme header)
├── functions.php (Theme functions)
├── index.php (Fallback template)
├── theme.json (Theme configuration)
├── templates/ (Block templates)
├── parts/ (Template parts)
├── patterns/ (Block patterns)
└── assets/ (CSS, JS, images)
```

### Adding New Patterns
1. Create a new PHP file in `/patterns/`
2. Follow the existing pattern structure
3. Register in `functions.php` if needed

### Customizing Templates
1. Edit files in `/templates/` directory
2. Use WordPress block markup
3. Reference patterns with `<!-- wp:pattern {"slug":"kyriakis/pattern-name"} /-->`

## 🐛 Troubleshooting

### Common Issues

**Pattern not showing:**
- Check if pattern file exists in `/patterns/`
- Verify pattern registration in `functions.php`
- Clear any caching

**Language switching not working:**
- Check JavaScript console for errors
- Verify element IDs match translation keys
- Ensure scripts are loading properly

**Styles not applying:**
- Clear browser cache
- Check if theme is properly activated
- Verify CSS files are loading

**Images not displaying:**
- Check image paths in patterns
- Ensure images are uploaded to Media Library
- Update image URLs to match your site

## 📞 Support

### Getting Help
- Check this README for common solutions
- Review WordPress Codex for block theme documentation
- Test in a staging environment first

### Customization Services
For custom modifications or additional features, consider hiring a WordPress developer familiar with block themes.

## 📄 License

This theme is licensed under GPL v2 or later. You are free to use, modify, and distribute it according to the GPL license terms.

## 🔄 Updates

### Version 1.0.0
- Initial release
- Full Gutenberg compatibility
- Bilingual support
- Modern design system
- Mobile optimization

---

**Ready to launch your professional plumbing website! 🚀**
