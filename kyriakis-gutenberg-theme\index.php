<?php
/**
 * The main template file
 * 
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 * It is used to display a page when nothing more specific matches a query.
 * 
 * @package KyriakisPlumber
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    <div class="kyriakis-container">
        <?php
        if (have_posts()) :
            while (have_posts()) :
                the_post();
                ?>
                <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                    <header class="entry-header">
                        <?php
                        if (is_singular()) :
                            the_title('<h1 class="entry-title">', '</h1>');
                        else :
                            the_title('<h2 class="entry-title"><a href="' . esc_url(get_permalink()) . '" rel="bookmark">', '</a></h2>');
                        endif;
                        ?>
                    </header>

                    <div class="entry-content">
                        <?php
                        if (is_singular()) :
                            the_content();
                        else :
                            the_excerpt();
                        endif;
                        ?>
                    </div>

                    <?php if (!is_singular()) : ?>
                        <footer class="entry-footer">
                            <a href="<?php echo esc_url(get_permalink()); ?>" class="read-more">
                                <?php _e('Read More', 'kyriakis-plumber'); ?>
                            </a>
                        </footer>
                    <?php endif; ?>
                </article>
                <?php
            endwhile;

            // Pagination
            the_posts_pagination(array(
                'prev_text' => __('Previous', 'kyriakis-plumber'),
                'next_text' => __('Next', 'kyriakis-plumber'),
            ));

        else :
            ?>
            <div class="no-posts">
                <h1><?php _e('Nothing Found', 'kyriakis-plumber'); ?></h1>
                <p><?php _e('It looks like nothing was found at this location. Maybe try a search?', 'kyriakis-plumber'); ?></p>
                <?php get_search_form(); ?>
            </div>
            <?php
        endif;
        ?>
    </div>
</main>

<?php
get_footer();
