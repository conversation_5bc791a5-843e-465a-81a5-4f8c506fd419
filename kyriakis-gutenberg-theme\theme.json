{"$schema": "https://schemas.wp.org/trunk/theme.json", "version": 2, "settings": {"appearanceTools": true, "useRootPaddingAwareAlignments": true, "color": {"custom": true, "customDuotone": true, "customGradient": true, "defaultDuotones": false, "defaultGradients": false, "defaultPalette": false, "duotone": [], "gradients": [{"slug": "primary-gradient", "gradient": "linear-gradient(135deg, #003b6f 0%, #2563eb 100%)", "name": "Primary Gradient"}, {"slug": "secondary-gradient", "gradient": "linear-gradient(45deg, #2563eb 0%, #9333ea 100%)", "name": "Secondary Gradient"}, {"slug": "success-gradient", "gradient": "linear-gradient(45deg, #10b981 0%, #059669 100%)", "name": "Success Gradient"}, {"slug": "glass-gradient", "gradient": "linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)", "name": "Glass Effect"}], "palette": [{"slug": "primary", "color": "#003b6f", "name": "Primary"}, {"slug": "secondary", "color": "#2563eb", "name": "Secondary"}, {"slug": "accent", "color": "#9333ea", "name": "Accent"}, {"slug": "success", "color": "#10b981", "name": "Success"}, {"slug": "warning", "color": "#f59e0b", "name": "Warning"}, {"slug": "error", "color": "#ef4444", "name": "Error"}, {"slug": "white", "color": "#ffffff", "name": "White"}, {"slug": "gray-50", "color": "#f9fafb", "name": "Gray 50"}, {"slug": "gray-100", "color": "#f3f4f6", "name": "Gray 100"}, {"slug": "gray-200", "color": "#e5e7eb", "name": "Gray 200"}, {"slug": "gray-300", "color": "#d1d5db", "name": "Gray 300"}, {"slug": "gray-400", "color": "#9ca3af", "name": "Gray 400"}, {"slug": "gray-500", "color": "#6b7280", "name": "Gray 500"}, {"slug": "gray-600", "color": "#4b5563", "name": "Gray 600"}, {"slug": "gray-700", "color": "#374151", "name": "Gray 700"}, {"slug": "gray-800", "color": "#1f2937", "name": "Gray 800"}, {"slug": "gray-900", "color": "#111827", "name": "Gray 900"}]}, "layout": {"contentSize": "1280px", "wideSize": "1400px"}, "spacing": {"blockGap": true, "margin": true, "padding": true, "units": ["px", "em", "rem", "vh", "vw", "%"]}, "typography": {"customFontSize": true, "dropCap": false, "fontStyle": true, "fontWeight": true, "letterSpacing": true, "lineHeight": true, "textDecoration": true, "textTransform": true, "fontFamilies": [{"fontFamily": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif", "name": "System Font", "slug": "system-font"}, {"fontFamily": "Georgia, serif", "name": "<PERSON><PERSON>", "slug": "serif"}], "fontSizes": [{"slug": "small", "size": "0.875rem", "name": "Small"}, {"slug": "medium", "size": "1rem", "name": "Medium"}, {"slug": "large", "size": "1.125rem", "name": "Large"}, {"slug": "x-large", "size": "1.25rem", "name": "Extra Large"}, {"slug": "xx-large", "size": "1.5rem", "name": "Extra Extra Large"}, {"slug": "xxx-large", "size": "2rem", "name": "<PERSON>ge"}]}, "border": {"color": true, "radius": true, "style": true, "width": true}}, "styles": {"color": {"background": "var(--wp--preset--color--white)", "text": "var(--wp--preset--color--gray-900)"}, "typography": {"fontFamily": "var(--wp--preset--font-family--system-font)", "fontSize": "var(--wp--preset--font-size--medium)", "lineHeight": "1.6"}, "spacing": {"blockGap": "1.5rem"}, "elements": {"link": {"color": {"text": "var(--wp--preset--color--secondary)"}, ":hover": {"color": {"text": "var(--wp--preset--color--primary)"}}}, "h1": {"typography": {"fontSize": "var(--wp--preset--font-size--xxx-large)", "fontWeight": "700", "lineHeight": "1.2"}}, "h2": {"typography": {"fontSize": "var(--wp--preset--font-size--xx-large)", "fontWeight": "600", "lineHeight": "1.3"}}, "h3": {"typography": {"fontSize": "var(--wp--preset--font-size--x-large)", "fontWeight": "600", "lineHeight": "1.4"}}, "h4": {"typography": {"fontSize": "var(--wp--preset--font-size--large)", "fontWeight": "600", "lineHeight": "1.4"}}, "h5": {"typography": {"fontSize": "var(--wp--preset--font-size--medium)", "fontWeight": "600", "lineHeight": "1.5"}}, "h6": {"typography": {"fontSize": "var(--wp--preset--font-size--small)", "fontWeight": "600", "lineHeight": "1.5"}}}, "blocks": {"core/button": {"border": {"radius": "1rem"}, "spacing": {"padding": {"top": "0.75rem", "right": "1.5rem", "bottom": "0.75rem", "left": "1.5rem"}}, "typography": {"fontWeight": "600"}}, "core/group": {"spacing": {"margin": {"top": "0", "bottom": "0"}}}, "core/columns": {"spacing": {"margin": {"top": "0", "bottom": "0"}}}}}, "templateParts": [{"name": "header", "title": "Header", "area": "header"}, {"name": "footer", "title": "Footer", "area": "footer"}]}