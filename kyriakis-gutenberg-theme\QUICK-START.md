# 🚀 Quick Start Guide - <PERSON><PERSON><PERSON><PERSON> Theme

## What You Now Have

✅ **Complete WordPress Gutenberg Theme** - Fully editable through WordPress editor
✅ **Bilingual Support** - Greek/English switching built-in
✅ **Modern Design** - Glassmorphism effects, gradients, animations
✅ **Mobile Responsive** - Works perfectly on all devices
✅ **SEO Optimized** - Clean code, proper structure
✅ **Block Patterns** - Pre-built sections you can easily insert

## 📥 Installation (5 Minutes)

### Step 1: Upload Theme to WordPress
1. Compress the `kyriakis-gutenberg-theme` folder into a ZIP file
2. In WordPress Admin: **Appearance > Themes > Add New > Upload Theme**
3. Upload the ZIP and click **Activate**

### Step 2: Upload Your Images
Go to **Media > Add New** and upload these images from your `public` folder:
- `Λεβητοστάσιο.jpg`
- `Ηλιακό συστήματα.jpg`
- `Ενδοδαπεδια θερμανση.jpg`
- `Θερμοσίφωνο.jpg`
- `Επαγγελματική επισκευή υδραυλικών προβλημάτων με Εγγύηση ποιότητας.jpg`
- `Εγκατάσταση νέων σωληνώσεων με σύγχρονα υλικά και τεχνικές.jpg`
- `logo-valsir-en-gb.png`
- `logo-aquatherm.png`
- `uponor.jpg`
- `rahau-sunergasia.jpg.webp`

### Step 3: Create Your Homepage
1. **Pages > Add New**
2. Title: "Home"
3. In the editor, click **+** then **Patterns**
4. Look for **"Kyriakis Plumber"** category
5. Add these patterns in order:
   - Header with Navigation
   - Hero Section
   - Footer

### Step 4: Set as Homepage
1. **Settings > Reading**
2. Select "A static page"
3. Choose "Home" as your homepage

## 🎨 How to Edit Your Site

### Adding Content Sections
1. Edit your homepage
2. Click **+** between existing blocks
3. Go to **Patterns > Kyriakis Plumber**
4. Insert any pattern you want

### Editing Existing Content
1. Click on any text to edit it directly
2. Click on images to replace them
3. Use the block toolbar for formatting options
4. Colors and styles are pre-configured

### Language Switching
- The language switcher in the header works automatically
- It switches between Greek and English
- All content translates dynamically

## 📞 Contact Information Setup

### Update Your Contact Details
In the Header and Footer patterns, update:
- Phone: `+306985814213` (replace with your number)
- Email: `<EMAIL>` (replace with your email)
- WhatsApp: `https://wa.me/306985814213` (replace with your number)

### Contact Form Options

**Option 1: Contact Form 7 (Easiest)**
1. Install Contact Form 7 plugin
2. Create a form with name, email, phone, message fields
3. Add the shortcode in a Custom HTML block

**Option 2: Direct Contact**
- Use the existing call and WhatsApp buttons
- Customers can call or message directly
- No form needed

## 🌐 Adding More Languages

The theme is ready for more languages. To add:
1. Find the JavaScript in each pattern
2. Add your language to the translations object
3. Update the language switcher buttons

## 🎯 Next Steps

### Essential Pages to Create
1. **Services** - Detail your plumbing services
2. **About** - Your story and experience
3. **Contact** - Contact form and information
4. **Privacy Policy** - Required for GDPR

### Recommended Plugins
- **Contact Form 7** - For contact forms
- **Yoast SEO** - For search engine optimization
- **WP Rocket** - For caching and speed
- **Wordfence** - For security

### Customization
- **Colors**: Go to **Appearance > Editor > Styles**
- **Typography**: Customize fonts and sizes
- **Layout**: Add/remove sections as needed

## 🔧 Quick Fixes

**If something doesn't look right:**
1. Clear your browser cache (Ctrl+F5)
2. Check if all images are uploaded
3. Make sure the theme is activated
4. Try editing in an incognito window

**If language switching doesn't work:**
1. Check browser console for JavaScript errors
2. Make sure all patterns are properly inserted
3. Verify element IDs match in the code

## 📱 Mobile Testing

Your site is mobile-optimized, but always test:
1. Open your site on your phone
2. Test the menu, buttons, and forms
3. Check that text is readable
4. Ensure images load properly

## 🚀 Launch Checklist

Before going live:
- [ ] All images uploaded and displaying
- [ ] Contact information updated
- [ ] Phone numbers and emails working
- [ ] Language switcher functioning
- [ ] Mobile version looks good
- [ ] Contact form working (if using one)
- [ ] SEO plugin installed and configured

## 💡 Pro Tips

1. **Use the Block Editor**: Everything is editable through WordPress
2. **Test on Mobile**: Most visitors will be on phones
3. **Keep It Simple**: Don't add too many sections
4. **Update Regularly**: Keep WordPress and plugins updated
5. **Backup First**: Always backup before making changes

---

**Your professional plumbing website is ready! 🎉**

Need help? The theme is designed to be user-friendly, but if you get stuck, refer to the full README.md file for detailed instructions.
