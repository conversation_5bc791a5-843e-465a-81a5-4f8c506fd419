<!-- WordPress Gutenberg Block: Contact Form Section -->
<!-- Copy this entire block into a Custom HTML block in WordPress -->
<!-- IMPORTANT: Replace YOUR_EMAILJS_SERVICE_ID and YOUR_EMAILJS_TEMPLATE_ID with your actual EmailJS credentials -->

<style>
.kyriakis-contact {
  padding: 5rem 0;
  background: #f9fafb;
}

.kyriakis-contact-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

.kyriakis-contact-form {
  background: white;
  border-radius: 1.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  padding: 2rem;
  border: 1px solid #f3f4f6;
  max-width: 96rem;
  margin: 0 auto;
}

@media (min-width: 1024px) {
  .kyriakis-contact-form {
    padding: 3rem;
  }
}

.kyriakis-contact-header {
  text-align: center;
  margin-bottom: 3rem;
}

.kyriakis-contact-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.kyriakis-contact-badge-icon {
  background: linear-gradient(45deg, #2563eb, #9333ea);
  padding: 0.75rem;
  border-radius: 1rem;
}

.kyriakis-contact-badge-text {
  color: #2563eb;
  font-weight: 600;
  font-size: 1.125rem;
}

.kyriakis-contact-title {
  font-size: 2.5rem;
  font-weight: bold;
  background: linear-gradient(45deg, #111827, #374151);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
}

@media (min-width: 1024px) {
  .kyriakis-contact-title {
    font-size: 3rem;
  }
}

.kyriakis-contact-description {
  font-size: 1.25rem;
  color: #4b5563;
  line-height: 1.6;
}

.kyriakis-contact-status {
  margin-bottom: 2rem;
  padding: 1.5rem;
  border-radius: 1rem;
  display: none;
}

.kyriakis-contact-status.success {
  background: linear-gradient(45deg, #d1fae5, #a7f3d0);
  border: 1px solid #10b981;
  display: block;
}

.kyriakis-contact-status.error {
  background: linear-gradient(45deg, #fecaca, #fca5a5);
  border: 1px solid #ef4444;
  display: block;
}

.kyriakis-contact-status-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.kyriakis-contact-status-icon {
  padding: 0.5rem;
  border-radius: 50%;
}

.kyriakis-contact-status.success .kyriakis-contact-status-icon {
  background: #10b981;
}

.kyriakis-contact-status.error .kyriakis-contact-status-icon {
  background: #ef4444;
}

.kyriakis-contact-status-text {
  font-weight: 600;
  font-size: 1.125rem;
}

.kyriakis-contact-status.success .kyriakis-contact-status-text {
  color: #065f46;
}

.kyriakis-contact-status.error .kyriakis-contact-status-text {
  color: #991b1b;
}

.kyriakis-contact-form-grid {
  display: grid;
  gap: 2rem;
  margin-bottom: 2rem;
}

.kyriakis-contact-form-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .kyriakis-contact-form-row {
    grid-template-columns: 1fr 1fr;
  }
}

.kyriakis-contact-form-group {
  margin-bottom: 0.5rem;
}

.kyriakis-contact-form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.75rem;
}

.kyriakis-contact-form-input,
.kyriakis-contact-form-select,
.kyriakis-contact-form-textarea {
  width: 100%;
  padding: 1rem 1.5rem;
  border: 2px solid #e5e7eb;
  border-radius: 1rem;
  font-size: 1.125rem;
  transition: all 0.3s ease;
  background: white;
}

.kyriakis-contact-form-input:hover,
.kyriakis-contact-form-select:hover,
.kyriakis-contact-form-textarea:hover {
  border-color: #93c5fd;
}

.kyriakis-contact-form-input:focus,
.kyriakis-contact-form-select:focus,
.kyriakis-contact-form-textarea:focus {
  outline: none;
  border-color: transparent;
  box-shadow: 0 0 0 2px #3b82f6;
}

.kyriakis-contact-form-textarea {
  resize: none;
  min-height: 120px;
}

.kyriakis-contact-form-buttons {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .kyriakis-contact-form-buttons {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

.kyriakis-contact-btn {
  padding: 1rem 2rem;
  border-radius: 1rem;
  font-weight: bold;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  border: none;
  cursor: pointer;
  font-size: 1rem;
}

.kyriakis-contact-btn:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: scale(1.05);
  text-decoration: none;
}

.kyriakis-contact-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.kyriakis-contact-btn-primary {
  background: linear-gradient(45deg, #2563eb, #06b6d4);
  color: white;
}

.kyriakis-contact-btn-primary:hover {
  background: linear-gradient(45deg, #1d4ed8, #0891b2);
  color: white;
}

.kyriakis-contact-btn-success {
  background: linear-gradient(45deg, #10b981, #059669);
  color: white;
}

.kyriakis-contact-btn-success:hover {
  background: linear-gradient(45deg, #059669, #047857);
  color: white;
}

.kyriakis-contact-btn-danger {
  background: linear-gradient(45deg, #ef4444, #ec4899);
  color: white;
}

.kyriakis-contact-btn-danger:hover {
  background: linear-gradient(45deg, #dc2626, #db2777);
  color: white;
}

.kyriakis-contact-info {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
}

.kyriakis-contact-info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
  text-align: center;
}

@media (min-width: 768px) {
  .kyriakis-contact-info-grid {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

.kyriakis-contact-info-item {
  transition: all 0.3s ease;
}

.kyriakis-contact-info-icon {
  padding: 1rem;
  border-radius: 1rem;
  width: fit-content;
  margin: 0 auto 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.kyriakis-contact-info-item:hover .kyriakis-contact-info-icon {
  transform: scale(1.1);
}

.kyriakis-contact-info-title {
  font-weight: bold;
  color: #111827;
  font-size: 1.125rem;
  margin-bottom: 0.5rem;
}

.kyriakis-contact-info-text {
  color: #4b5563;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.kyriakis-contact-info-subtext {
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.kyriakis-contact-info-subtext.blue {
  color: #2563eb;
}

.kyriakis-contact-info-subtext.green {
  color: #10b981;
}

.kyriakis-contact-info-subtext.purple {
  color: #9333ea;
}

.kyriakis-contact-social {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
}

.kyriakis-contact-social-btn {
  background: #10b981;
  color: white;
  padding: 0.5rem;
  border-radius: 0.75rem;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kyriakis-contact-social-btn:hover {
  background: #059669;
  transform: scale(1.1);
  color: white;
  text-decoration: none;
}

/* Icons */
.kyriakis-icon {
  width: 1.5rem;
  height: 1.5rem;
  fill: currentColor;
}

.kyriakis-icon-lg {
  width: 2rem;
  height: 2rem;
  fill: currentColor;
}
</style>

<section id="contact" class="kyriakis-contact">
  <div class="kyriakis-contact-container">
    <div class="kyriakis-contact-form">
      <div class="kyriakis-contact-header">
        <div class="kyriakis-contact-badge">
          <div class="kyriakis-contact-badge-icon">
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
              <polyline points="22,6 12,13 2,6"/>
            </svg>
          </div>
          <span class="kyriakis-contact-badge-text" id="contact-badge">Επικοινωνία 2025</span>
        </div>
        <h2 class="kyriakis-contact-title" id="contact-title">Επικοινωνήστε Μαζί Μας</h2>
        <p class="kyriakis-contact-description" id="contact-description">
          Στείλτε μας μήνυμα και θα επικοινωνήσουμε άμεσα μαζί σας με προσωπική εξυπηρέτηση
        </p>
      </div>

      <!-- Status Messages -->
      <div id="contact-status" class="kyriakis-contact-status">
        <div class="kyriakis-contact-status-content">
          <div class="kyriakis-contact-status-icon">
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
            </svg>
          </div>
          <p class="kyriakis-contact-status-text" id="contact-status-text"></p>
        </div>
      </div>

      <form id="contact-form" class="kyriakis-contact-form-grid">
        <div class="kyriakis-contact-form-row">
          <div class="kyriakis-contact-form-group">
            <label for="contact-name" class="kyriakis-contact-form-label" id="contact-name-label">Όνομα *</label>
            <input
              type="text"
              id="contact-name"
              name="name"
              required
              class="kyriakis-contact-form-input"
              placeholder="Όνομα"
            >
          </div>

          <div class="kyriakis-contact-form-group">
            <label for="contact-email" class="kyriakis-contact-form-label" id="contact-email-label">Email *</label>
            <input
              type="email"
              id="contact-email"
              name="email"
              required
              class="kyriakis-contact-form-input"
              placeholder="<EMAIL>"
            >
          </div>
        </div>

        <div class="kyriakis-contact-form-group">
          <label for="contact-phone" class="kyriakis-contact-form-label" id="contact-phone-label">Τηλέφωνο *</label>
          <input
            type="tel"
            id="contact-phone"
            name="phone"
            required
            class="kyriakis-contact-form-input"
            placeholder="************"
          >
        </div>

        <div class="kyriakis-contact-form-group">
          <label for="contact-service" class="kyriakis-contact-form-label" id="contact-service-label">Υπηρεσία</label>
          <select id="contact-service" name="service" class="kyriakis-contact-form-select">
            <option value="" id="contact-select-option">Επιλέξτε υπηρεσία</option>
            <option value="Εγκατάσταση Λεβητοστασίου">Εγκατάσταση Λεβητοστασίου</option>
            <option value="Service Λεβητοστασίου">Service Λεβητοστασίου</option>
            <option value="Ηλιακά Συστήματα">Ηλιακά Συστήματα</option>
            <option value="Ενδοδαπέδια Θέρμανση">Ενδοδαπέδια Θέρμανση</option>
            <option value="Θερμοσίφωνο">Θερμοσίφωνο</option>
            <option value="Επισκευή Υδραυλικών">Επισκευή Υδραυλικών</option>
            <option value="Εγκατάσταση σωληνώσεων">Εγκατάσταση σωληνώσεων</option>
            <option value="Άλλο">Άλλο</option>
          </select>
        </div>

        <div class="kyriakis-contact-form-group">
          <label for="contact-message" class="kyriakis-contact-form-label" id="contact-message-label">Μήνυμα</label>
          <textarea
            id="contact-message"
            name="message"
            rows="5"
            class="kyriakis-contact-form-textarea"
            placeholder="Περιγράψτε το πρόβλημα ή την υπηρεσία που χρειάζεστε..."
            id="contact-placeholder"
          ></textarea>
        </div>

        <div class="kyriakis-contact-form-buttons">
          <button
            type="submit"
            data-type="contact"
            class="kyriakis-contact-btn kyriakis-contact-btn-primary"
            id="contact-send-btn"
          >
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="22" y1="2" x2="11" y2="13"/>
              <polygon points="22,2 15,22 11,13 2,9 22,2"/>
            </svg>
            <span id="contact-send-text">Στείλτε Μήνυμα</span>
          </button>

          <button
            type="submit"
            data-type="quote"
            class="kyriakis-contact-btn kyriakis-contact-btn-success"
            id="contact-quote-btn"
          >
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/>
            </svg>
            <span id="contact-quote-text">Ζητήστε Προσφορά</span>
          </button>

          <button
            type="submit"
            data-type="emergency"
            class="kyriakis-contact-btn kyriakis-contact-btn-danger"
            id="contact-emergency-btn"
          >
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
              <line x1="12" y1="9" x2="12" y2="13"/>
              <line x1="12" y1="17" x2="12.01" y2="17"/>
            </svg>
            <span id="contact-emergency-text">Επείγον</span>
          </button>
        </div>
      </form>

      <div class="kyriakis-contact-info">
        <div class="kyriakis-contact-info-grid">
          <div class="kyriakis-contact-info-item">
            <div class="kyriakis-contact-info-icon" style="background: linear-gradient(45deg, #3b82f6, #06b6d4);">
              <svg class="kyriakis-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
              </svg>
            </div>
            <h3 class="kyriakis-contact-info-title" id="contact-phone-title">Τηλέφωνο</h3>
            <p class="kyriakis-contact-info-text">+30 ************</p>
            <p class="kyriakis-contact-info-subtext blue" id="contact-phone-available">24/7 Διαθέσιμο</p>
            <div class="kyriakis-contact-social">
              <a
                href="https://wa.me/306985814213"
                target="_blank"
                class="kyriakis-contact-social-btn"
                title="WhatsApp"
              >
                <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"/>
                </svg>
              </a>
            </div>
          </div>
          
          <div class="kyriakis-contact-info-item">
            <div class="kyriakis-contact-info-icon" style="background: linear-gradient(45deg, #10b981, #059669);">
              <svg class="kyriakis-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                <polyline points="22,6 12,13 2,6"/>
              </svg>
            </div>
            <h3 class="kyriakis-contact-info-title" id="contact-email-title">Email</h3>
            <p class="kyriakis-contact-info-text"><EMAIL></p>
            <p class="kyriakis-contact-info-subtext green" id="contact-email-fast">Γρήγορη Απάντηση</p>
          </div>
          
          <div class="kyriakis-contact-info-item">
            <div class="kyriakis-contact-info-icon" style="background: linear-gradient(45deg, #9333ea, #ec4899);">
              <svg class="kyriakis-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12,6 12,12 16,14"/>
              </svg>
            </div>
            <h3 class="kyriakis-contact-info-title" id="contact-hours-title">Ώρες Λειτουργίας</h3>
            <p class="kyriakis-contact-info-text" id="contact-hours-schedule">Δευτ-Παρ: 8:00-18:00</p>
            <p class="kyriakis-contact-info-subtext purple" id="contact-hours-emergency">24/7 Επείγοντα</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- EmailJS Script -->
<script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>

<script>
// Initialize EmailJS - REPLACE WITH YOUR ACTUAL PUBLIC KEY
emailjs.init('YOUR_EMAILJS_PUBLIC_KEY');

// Contact translations
const contactTranslations = {
  gr: {
    'contact-badge': 'Επικοινωνία 2025',
    'contact-title': 'Επικοινωνήστε Μαζί Μας',
    'contact-description': 'Στείλτε μας μήνυμα και θα επικοινωνήσουμε άμεσα μαζί σας με προσωπική εξυπηρέτηση',
    'contact-name-label': 'Όνομα *',
    'contact-email-label': 'Email *',
    'contact-phone-label': 'Τηλέφωνο *',
    'contact-service-label': 'Υπηρεσία',
    'contact-message-label': 'Μήνυμα',
    'contact-select-option': 'Επιλέξτε υπηρεσία',
    'contact-send-text': 'Στείλτε Μήνυμα',
    'contact-quote-text': 'Ζητήστε Προσφορά',
    'contact-emergency-text': 'Επείγον',
    'contact-sending': 'Αποστολή...',
    'contact-success': '✅ Το μήνυμά σας στάλθηκε επιτυχώς!',
    'contact-error': '❌ Υπήρξε πρόβλημα. Παρακαλώ δοκιμάστε ξανά.',
    'contact-phone-title': 'Τηλέφωνο',
    'contact-phone-available': '24/7 Διαθέσιμο',
    'contact-email-title': 'Email',
    'contact-email-fast': 'Γρήγορη Απάντηση',
    'contact-hours-title': 'Ώρες Λειτουργίας',
    'contact-hours-schedule': 'Δευτ-Παρ: 8:00-18:00',
    'contact-hours-emergency': '24/7 Επείγοντα'
  },
  en: {
    'contact-badge': '2025 Contact',
    'contact-title': 'Contact Us',
    'contact-description': 'Send us a message and we will contact you immediately with personalized service',
    'contact-name-label': 'Name *',
    'contact-email-label': 'Email *',
    'contact-phone-label': 'Phone *',
    'contact-service-label': 'Service',
    'contact-message-label': 'Message',
    'contact-select-option': 'Select service',
    'contact-send-text': 'Send Message',
    'contact-quote-text': 'Request Quote',
    'contact-emergency-text': 'Emergency',
    'contact-sending': 'Sending...',
    'contact-success': '✅ Your message was sent successfully!',
    'contact-error': '❌ There was a problem. Please try again.',
    'contact-phone-title': 'Phone',
    'contact-phone-available': '24/7 Available',
    'contact-email-title': 'Email',
    'contact-email-fast': 'Fast Response',
    'contact-hours-title': 'Working Hours',
    'contact-hours-schedule': 'Mon-Fri: 8:00-18:00',
    'contact-hours-emergency': '24/7 Emergency'
  }
};

let currentLang = 'gr';
let isSubmitting = false;

// Form submission handler
document.getElementById('contact-form').addEventListener('submit', async function(e) {
  e.preventDefault();
  
  if (isSubmitting) return;
  
  const clickedButton = e.submitter;
  const formType = clickedButton.getAttribute('data-type') || 'contact';
  
  // Get form data
  const formData = {
    name: document.getElementById('contact-name').value,
    email: document.getElementById('contact-email').value,
    phone: document.getElementById('contact-phone').value,
    service: document.getElementById('contact-service').value,
    message: document.getElementById('contact-message').value,
    type: formType
  };
  
  // Validate required fields
  if (!formData.name || !formData.email || !formData.phone) {
    showStatus('error', contactTranslations[currentLang]['contact-error']);
    return;
  }
  
  // Set submitting state
  isSubmitting = true;
  updateButtonStates(true);
  
  try {
    // REPLACE WITH YOUR ACTUAL SERVICE ID AND TEMPLATE ID
    const response = await emailjs.send(
      'YOUR_EMAILJS_SERVICE_ID',
      'YOUR_EMAILJS_TEMPLATE_ID',
      {
        from_name: formData.name,
        from_email: formData.email,
        phone: formData.phone,
        service: formData.service,
        type: formData.type,
        message: formData.message,
        to_name: 'Kyriakis Plumber'
      }
    );
    
    if (response.status === 200) {
      showStatus('success', contactTranslations[currentLang]['contact-success']);
      document.getElementById('contact-form').reset();
    } else {
      showStatus('error', contactTranslations[currentLang]['contact-error']);
    }
  } catch (error) {
    console.error('EmailJS Error:', error);
    showStatus('error', contactTranslations[currentLang]['contact-error']);
  } finally {
    isSubmitting = false;
    updateButtonStates(false);
  }
});

// Show status message
function showStatus(type, message) {
  const statusEl = document.getElementById('contact-status');
  const statusTextEl = document.getElementById('contact-status-text');
  
  statusEl.className = `kyriakis-contact-status ${type}`;
  statusTextEl.textContent = message;
  
  // Auto hide after 5 seconds
  setTimeout(() => {
    statusEl.className = 'kyriakis-contact-status';
  }, 5000);
}

// Update button states
function updateButtonStates(submitting) {
  const buttons = document.querySelectorAll('.kyriakis-contact-btn');
  const sendingText = contactTranslations[currentLang]['contact-sending'];
  
  buttons.forEach(button => {
    button.disabled = submitting;
    if (submitting) {
      const textSpan = button.querySelector('span');
      textSpan.textContent = sendingText;
    } else {
      // Restore original text
      const buttonId = button.id;
      if (buttonId === 'contact-send-btn') {
        button.querySelector('span').textContent = contactTranslations[currentLang]['contact-send-text'];
      } else if (buttonId === 'contact-quote-btn') {
        button.querySelector('span').textContent = contactTranslations[currentLang]['contact-quote-text'];
      } else if (buttonId === 'contact-emergency-btn') {
        button.querySelector('span').textContent = contactTranslations[currentLang]['contact-emergency-text'];
      }
    }
  });
}

// Listen for language changes
window.addEventListener('languageChanged', function(e) {
  currentLang = e.detail.language;
  const translations = contactTranslations[currentLang];
  
  Object.keys(translations).forEach(key => {
    const element = document.getElementById(key);
    if (element) {
      if (key === 'contact-placeholder') {
        element.placeholder = translations[key];
      } else {
        element.textContent = translations[key];
      }
    }
  });
  
  // Update service options
  const serviceSelect = document.getElementById('contact-service');
  const options = serviceSelect.querySelectorAll('option');
  
  if (currentLang === 'en') {
    options[0].textContent = 'Select service';
    options[1].textContent = 'Boiler Installation';
    options[2].textContent = 'Boiler Service';
    options[3].textContent = 'Solar Systems';
    options[4].textContent = 'Underfloor Heating';
    options[5].textContent = 'Water Heater';
    options[6].textContent = 'Plumbing Repair';
    options[7].textContent = 'Pipe Installation';
    options[8].textContent = 'Other';
  } else {
    options[0].textContent = 'Επιλέξτε υπηρεσία';
    options[1].textContent = 'Εγκατάσταση Λεβητοστασίου';
    options[2].textContent = 'Service Λεβητοστασίου';
    options[3].textContent = 'Ηλιακά Συστήματα';
    options[4].textContent = 'Ενδοδαπέδια Θέρμανση';
    options[5].textContent = 'Θερμοσίφωνο';
    options[6].textContent = 'Επισκευή Υδραυλικών';
    options[7].textContent = 'Εγκατάσταση σωληνώσεων';
    options[8].textContent = 'Άλλο';
  }
  
  // Update placeholder
  const messageTextarea = document.getElementById('contact-message');
  if (currentLang === 'en') {
    messageTextarea.placeholder = 'Describe the problem or service you need...';
  } else {
    messageTextarea.placeholder = 'Περιγράψτε το πρόβλημα ή την υπηρεσία που χρειάζεστε...';
  }
});
</script>

<!-- 
SETUP INSTRUCTIONS:
1. Sign up for EmailJS at https://www.emailjs.com/
2. Create a service (Gmail, Outlook, etc.)
3. Create an email template
4. Replace the following in the script above:
   - YOUR_EMAILJS_PUBLIC_KEY with your actual public key
   - YOUR_EMAILJS_SERVICE_ID with your actual service ID
   - YOUR_EMAILJS_TEMPLATE_ID with your actual template ID

Template variables to use in EmailJS:
- {{from_name}} - Sender's name
- {{from_email}} - Sender's email
- {{phone}} - Phone number
- {{service}} - Selected service
- {{type}} - Form type (contact/quote/emergency)
- {{message}} - Message content
- {{to_name}} - Your company name
-->