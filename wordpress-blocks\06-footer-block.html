<!-- Word<PERSON>ress Gutenberg Block: Footer Section -->
<!-- Copy this entire block into a Custom HTML block in WordPress -->

<style>
.kyriakis-footer {
  background: linear-gradient(135deg, #111827 0%, #1f2937 50%, #111827 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.kyriakis-footer::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
}

.kyriakis-footer-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.kyriakis-footer-bg-circle {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
}

.kyriakis-footer-bg-circle:nth-child(1) {
  top: 5rem;
  left: 5rem;
  width: 16rem;
  height: 16rem;
  background: rgba(59, 130, 246, 0.05);
}

.kyriakis-footer-bg-circle:nth-child(2) {
  bottom: 5rem;
  right: 5rem;
  width: 20rem;
  height: 20rem;
  background: rgba(147, 51, 234, 0.05);
}

.kyriakis-footer-container {
  position: relative;
  max-width: 1280px;
  margin: 0 auto;
  padding: 5rem 1rem;
  z-index: 10;
}

.kyriakis-footer-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
}

@media (min-width: 768px) {
  .kyriakis-footer-content {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 1024px) {
  .kyriakis-footer-content {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }
}

.kyriakis-footer-section {
  margin-bottom: 2rem;
}

.kyriakis-footer-logo {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.kyriakis-footer-logo:hover {
  transform: scale(1.05);
}

.kyriakis-footer-logo-icon {
  position: relative;
}

.kyriakis-footer-logo-main {
  background: linear-gradient(135deg, #2563eb, #9333ea);
  padding: 1rem;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
}

.kyriakis-footer-logo:hover .kyriakis-footer-logo-main {
  box-shadow: 0 25px 50px -12px rgba(59, 130, 246, 0.4);
}

.kyriakis-footer-logo-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: linear-gradient(45deg, #fbbf24, #f97316);
  border-radius: 50%;
  padding: 0.25rem;
}

.kyriakis-footer-logo-text h3 {
  font-size: 1.5rem;
  font-weight: bold;
  background: linear-gradient(45deg, #ffffff, #dbeafe);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 0.25rem 0;
}

.kyriakis-footer-logo-text p {
  color: #93c5fd;
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0;
}

.kyriakis-footer-description {
  color: #d1d5db;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.kyriakis-footer-title {
  font-size: 1.25rem;
  font-weight: bold;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.kyriakis-footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.kyriakis-footer-links li {
  margin-bottom: 1rem;
}

.kyriakis-footer-link {
  color: #d1d5db;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.kyriakis-footer-link:hover {
  color: white;
  transform: translateX(8px);
  text-decoration: none;
}

.kyriakis-footer-link-dot {
  width: 0.5rem;
  height: 0.5rem;
  background: #3b82f6;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.kyriakis-footer-link:hover .kyriakis-footer-link-dot {
  background: #fbbf24;
}

.kyriakis-footer-service-link {
  color: #d1d5db;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.kyriakis-footer-service-link:hover {
  color: white;
}

.kyriakis-footer-service-dot {
  width: 0.25rem;
  height: 0.25rem;
  background: #9333ea;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.kyriakis-footer-service-link:hover .kyriakis-footer-service-dot {
  background: #06b6d4;
}

.kyriakis-footer-contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.kyriakis-footer-contact-item:hover {
  transform: scale(1.05);
}

.kyriakis-footer-contact-icon {
  padding: 0.75rem;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.kyriakis-footer-contact-item:hover .kyriakis-footer-contact-icon {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.kyriakis-footer-contact-info h4 {
  color: white;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.kyriakis-footer-contact-info p {
  color: #9ca3af;
  font-size: 0.875rem;
  margin: 0;
}

.kyriakis-footer-social {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.kyriakis-footer-social-btn {
  background: #10b981;
  color: white;
  padding: 0.375rem;
  border-radius: 0.5rem;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kyriakis-footer-social-btn:hover {
  background: #059669;
  transform: scale(1.1);
  color: white;
  text-decoration: none;
}

.kyriakis-footer-bottom {
  border-top: 1px solid #374151;
  margin-top: 4rem;
  padding-top: 2rem;
}

.kyriakis-footer-bottom-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

@media (min-width: 768px) {
  .kyriakis-footer-bottom-content {
    flex-direction: row;
  }
}

.kyriakis-footer-copyright {
  color: #9ca3af;
  font-size: 0.875rem;
  text-align: center;
}

@media (min-width: 768px) {
  .kyriakis-footer-copyright {
    text-align: left;
  }
}

.kyriakis-footer-legal {
  display: flex;
  gap: 2rem;
}

.kyriakis-footer-legal-link {
  color: #9ca3af;
  text-decoration: none;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.kyriakis-footer-legal-link:hover {
  color: white;
  text-decoration: underline;
}

/* Icons */
.kyriakis-icon {
  width: 1.5rem;
  height: 1.5rem;
  fill: currentColor;
}

.kyriakis-icon-sm {
  width: 1rem;
  height: 1rem;
  fill: currentColor;
}

.kyriakis-icon-lg {
  width: 2rem;
  height: 2rem;
  fill: currentColor;
}
</style>

<footer class="kyriakis-footer">
  <!-- Background Effects -->
  <div class="kyriakis-footer-bg">
    <div class="kyriakis-footer-bg-circle"></div>
    <div class="kyriakis-footer-bg-circle"></div>
  </div>

  <div class="kyriakis-footer-container">
    <div class="kyriakis-footer-content">
      <!-- Company Info -->
      <div class="kyriakis-footer-section">
        <div class="kyriakis-footer-logo" onclick="scrollToSection('home')">
          <div class="kyriakis-footer-logo-icon">
            <div class="kyriakis-footer-logo-main">
              <svg class="kyriakis-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/>
              </svg>
            </div>
            <div class="kyriakis-footer-logo-badge">
              <svg class="kyriakis-icon-sm" viewBox="0 0 24 24" fill="currentColor">
                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
              </svg>
            </div>
          </div>
          <div class="kyriakis-footer-logo-text">
            <h3>Kyriakis Plumber</h3>
            <p id="footer-premium">Premium Services 2025</p>
          </div>
        </div>
        
        <p class="kyriakis-footer-description" id="footer-description">
          Επαγγελματικές θερμοϋδραυλικές υπηρεσίες με περισσότερα από 30 χρόνια εμπειρίας. Εξυπηρετούμε όλη την Ελλάδα με εγγύηση ποιότητας και τεχνολογία αιχμής.
        </p>
      </div>

      <!-- Quick Links -->
      <div class="kyriakis-footer-section">
        <h4 class="kyriakis-footer-title">
          <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
          </svg>
          <span id="footer-links">Γρήγοροι Σύνδεσμοι</span>
        </h4>
        <ul class="kyriakis-footer-links">
          <li>
            <div class="kyriakis-footer-link" onclick="scrollToSection('home')">
              <div class="kyriakis-footer-link-dot"></div>
              <span id="footer-home">Αρχική</span>
            </div>
          </li>
          <li>
            <div class="kyriakis-footer-link" onclick="scrollToSection('services')">
              <div class="kyriakis-footer-link-dot"></div>
              <span id="footer-services">Υπηρεσίες</span>
            </div>
          </li>
          <li>
            <div class="kyriakis-footer-link" onclick="scrollToSection('about')">
              <div class="kyriakis-footer-link-dot"></div>
              <span id="footer-about">Σχετικά</span>
            </div>
          </li>
          <li>
            <div class="kyriakis-footer-link" onclick="scrollToSection('contact')">
              <div class="kyriakis-footer-link-dot"></div>
              <span id="footer-contact">Επικοινωνία</span>
            </div>
          </li>
        </ul>
      </div>

      <!-- Services -->
      <div class="kyriakis-footer-section">
        <h4 class="kyriakis-footer-title">
          <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/>
          </svg>
          <span id="footer-services-title">Υπηρεσίες</span>
        </h4>
        <div>
          <div class="kyriakis-footer-service-link" onclick="scrollToSection('services')">
            <div class="kyriakis-footer-service-dot"></div>
            Εγκατάσταση Λεβητοστασίου
          </div>
          <div class="kyriakis-footer-service-link" onclick="scrollToSection('services')">
            <div class="kyriakis-footer-service-dot"></div>
            Service Λεβητοστασίου
          </div>
          <div class="kyriakis-footer-service-link" onclick="scrollToSection('services')">
            <div class="kyriakis-footer-service-dot"></div>
            Ηλιακά Συστήματα
          </div>
          <div class="kyriakis-footer-service-link" onclick="scrollToSection('services')">
            <div class="kyriakis-footer-service-dot"></div>
            Ενδοδαπέδια Θέρμανση
          </div>
          <div class="kyriakis-footer-service-link" onclick="scrollToSection('services')">
            <div class="kyriakis-footer-service-dot"></div>
            Θερμοσίφωνο
          </div>
          <div class="kyriakis-footer-service-link" onclick="scrollToSection('services')">
            <div class="kyriakis-footer-service-dot"></div>
            Επισκευή Υδραυλικών
          </div>
        </div>
      </div>

      <!-- Contact Info -->
      <div class="kyriakis-footer-section">
        <h4 class="kyriakis-footer-title">
          <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
          </svg>
          <span id="footer-contact-title">Επικοινωνία</span>
        </h4>
        
        <div class="kyriakis-footer-contact-item">
          <div class="kyriakis-footer-contact-icon" style="background: linear-gradient(45deg, #10b981, #059669);">
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
            </svg>
          </div>
          <div class="kyriakis-footer-contact-info">
            <h4>+30 ************</h4>
            <p id="footer-phone-available">24/7 Επείγοντα</p>
            <div class="kyriakis-footer-social">
              <a
                href="https://wa.me/306985814213"
                target="_blank"
                class="kyriakis-footer-social-btn"
                title="WhatsApp"
              >
                <svg class="kyriakis-icon-sm" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"/>
                </svg>
              </a>
            </div>
          </div>
        </div>

        <div class="kyriakis-footer-contact-item">
          <div class="kyriakis-footer-contact-icon" style="background: linear-gradient(45deg, #3b82f6, #06b6d4);">
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
              <polyline points="22,6 12,13 2,6"/>
            </svg>
          </div>
          <div class="kyriakis-footer-contact-info">
            <h4><EMAIL></h4>
            <p id="footer-email-fast">Γρήγορη Απάντηση</p>
          </div>
        </div>

        <div class="kyriakis-footer-contact-item">
          <div class="kyriakis-footer-contact-icon" style="background: linear-gradient(45deg, #9333ea, #ec4899);">
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
              <circle cx="12" cy="10" r="3"/>
            </svg>
          </div>
          <div class="kyriakis-footer-contact-info">
            <h4 id="footer-location">Αθήνα, Ελλάδα</h4>
            <p id="footer-serve">Εξυπηρετούμε όλη την Ελλάδα</p>
          </div>
        </div>

        <div class="kyriakis-footer-contact-item">
          <div class="kyriakis-footer-contact-icon" style="background: linear-gradient(45deg, #fbbf24, #f97316);">
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <polyline points="12,6 12,12 16,14"/>
            </svg>
          </div>
          <div class="kyriakis-footer-contact-info">
            <h4 id="footer-schedule">Δευτ-Παρ: 8:00-18:00</h4>
            <p id="footer-weekend">Σαββατοκύριακο: Επείγοντα</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom Bar -->
    <div class="kyriakis-footer-bottom">
      <div class="kyriakis-footer-bottom-content">
        <p class="kyriakis-footer-copyright" id="footer-copyright">
          © 2025 Kyriakis Plumber. Όλα τα δικαιώματα διατηρούνται. Designed with ❤️ for the future.
        </p>
        <div class="kyriakis-footer-legal">
          <a href="#" class="kyriakis-footer-legal-link" id="footer-terms">Όροι Χρήσης</a>
          <a href="#" class="kyriakis-footer-legal-link" id="footer-privacy">Πολιτική Απορρήτου</a>
          <a href="#" class="kyriakis-footer-legal-link" id="footer-cookies">Cookies</a>
        </div>
      </div>
    </div>
  </div>
</footer>

<script>
// Footer translations
const footerTranslations = {
  gr: {
    'footer-premium': 'Premium Services 2025',
    'footer-description': 'Επαγγελματικές θερμοϋδραυλικές υπηρεσίες με περισσότερα από 30 χρόνια εμπειρίας. Εξυπηρετούμε όλη την Ελλάδα με εγγύηση ποιότητας και τεχνολογία αιχμής.',
    'footer-links': 'Γρήγοροι Σύνδεσμοι',
    'footer-home': 'Αρχική',
    'footer-services': 'Υπηρεσίες',
    'footer-about': 'Σχετικά',
    'footer-contact': 'Επικοινωνία',
    'footer-services-title': 'Υπηρεσίες',
    'footer-contact-title': 'Επικοινωνία',
    'footer-phone-available': '24/7 Επείγοντα',
    'footer-email-fast': 'Γρήγορη Απάντηση',
    'footer-location': 'Αθήνα, Ελλάδα',
    'footer-serve': 'Εξυπηρετούμε όλη την Ελλάδα',
    'footer-schedule': 'Δευτ-Παρ: 8:00-18:00',
    'footer-weekend': 'Σαββατοκύριακο: Επείγοντα',
    'footer-copyright': '© 2025 Kyriakis Plumber. Όλα τα δικαιώματα διατηρούνται. Designed with ❤️ for the future.',
    'footer-terms': 'Όροι Χρήσης',
    'footer-privacy': 'Πολιτική Απορρήτου',
    'footer-cookies': 'Cookies'
  },
  en: {
    'footer-premium': 'Premium Services 2025',
    'footer-description': 'Professional plumbing and heating services with more than 30 years of experience. We serve all of Greece with quality guarantee and cutting-edge technology.',
    'footer-links': 'Quick Links',
    'footer-home': 'Home',
    'footer-services': 'Services',
    'footer-about': 'About',
    'footer-contact': 'Contact',
    'footer-services-title': 'Services',
    'footer-contact-title': 'Contact',
    'footer-phone-available': '24/7 Emergency',
    'footer-email-fast': 'Fast Response',
    'footer-location': 'Athens, Greece',
    'footer-serve': 'We serve all of Greece',
    'footer-schedule': 'Mon-Fri: 8:00-18:00',
    'footer-weekend': 'Weekend: Emergency',
    'footer-copyright': '© 2025 Kyriakis Plumber. All rights reserved. Designed with ❤️ for the future.',
    'footer-terms': 'Terms of Use',
    'footer-privacy': 'Privacy Policy',
    'footer-cookies': 'Cookies'
  }
};

// Listen for language changes
window.addEventListener('languageChanged', function(e) {
  const lang = e.detail.language;
  const translations = footerTranslations[lang];
  
  Object.keys(translations).forEach(key => {
    const element = document.getElementById(key);
    if (element) {
      element.textContent = translations[key];
    }
  });
});

// Smooth scroll function
function scrollToSection(sectionId) {
  const element = document.getElementById(sectionId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
}
</script>