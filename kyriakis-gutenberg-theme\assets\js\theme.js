/**
 * Kyriakis Plumber Theme JavaScript
 * 
 * @package KyriakisPlumber
 * @since 1.0.0
 */

(function() {
    'use strict';

    // Global variables
    let currentLanguage = 'gr';
    
    // Initialize theme
    document.addEventListener('DOMContentLoaded', function() {
        initializeTheme();
        initializeLanguageSwitcher();
        initializeScrollEffects();
        initializeMobileMenu();
        initializeContactForm();
    });

    /**
     * Initialize theme functionality
     */
    function initializeTheme() {
        // Add theme class to body
        document.body.classList.add('kyriakis-theme-loaded');
        
        // Initialize smooth scrolling for anchor links
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        anchorLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                scrollToSection(targetId);
            });
        });
    }

    /**
     * Initialize language switcher
     */
    function initializeLanguageSwitcher() {
        // Set initial language
        switchLanguage('gr');
        
        // Listen for language change events
        window.addEventListener('languageChanged', function(e) {
            currentLanguage = e.detail.language;
            updatePageLanguage(currentLanguage);
        });
    }

    /**
     * Initialize scroll effects
     */
    function initializeScrollEffects() {
        let ticking = false;
        
        function updateScrollEffects() {
            const scrollY = window.scrollY;
            
            // Header scroll effect
            const header = document.getElementById('kyriakis-header');
            if (header) {
                if (scrollY > 20) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            }
            
            // Parallax effects for hero section
            const hero = document.querySelector('.kyriakis-hero');
            if (hero) {
                const heroHeight = hero.offsetHeight;
                const scrollPercent = Math.min(scrollY / heroHeight, 1);
                
                // Update background circles
                const circles = hero.querySelectorAll('.kyriakis-hero-bg-circle');
                circles.forEach((circle, index) => {
                    const speed = 0.5 + (index * 0.2);
                    circle.style.transform = `translateY(${scrollPercent * 100 * speed}px)`;
                });
            }
            
            ticking = false;
        }
        
        function requestScrollUpdate() {
            if (!ticking) {
                requestAnimationFrame(updateScrollEffects);
                ticking = true;
            }
        }
        
        window.addEventListener('scroll', requestScrollUpdate);
        updateScrollEffects(); // Initial call
    }

    /**
     * Initialize mobile menu
     */
    function initializeMobileMenu() {
        // Mobile menu is handled by individual block patterns
        // This function can be extended for global mobile menu functionality
    }

    /**
     * Initialize contact form
     */
    function initializeContactForm() {
        const contactForms = document.querySelectorAll('.kyriakis-contact-form');
        
        contactForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                handleContactFormSubmission(this);
            });
        });
    }

    /**
     * Handle contact form submission
     */
    function handleContactFormSubmission(form) {
        const formData = new FormData(form);
        const submitButton = form.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        
        // Show loading state
        submitButton.textContent = currentLanguage === 'gr' ? 'Αποστολή...' : 'Sending...';
        submitButton.disabled = true;
        
        // Here you would typically send the form data to your server
        // For now, we'll simulate a successful submission
        setTimeout(() => {
            showNotification(
                currentLanguage === 'gr' ? 'Το μήνυμά σας στάλθηκε επιτυχώς!' : 'Your message was sent successfully!',
                'success'
            );
            
            form.reset();
            submitButton.textContent = originalText;
            submitButton.disabled = false;
        }, 2000);
    }

    /**
     * Show notification
     */
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `kyriakis-notification kyriakis-notification-${type}`;
        notification.textContent = message;
        
        // Style the notification
        Object.assign(notification.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#2563eb',
            color: 'white',
            padding: '1rem 1.5rem',
            borderRadius: '0.75rem',
            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
            zIndex: '9999',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 5 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }

    /**
     * Smooth scroll to section
     */
    function scrollToSection(sectionId) {
        const element = document.getElementById(sectionId);
        if (element) {
            const headerHeight = document.getElementById('kyriakis-header')?.offsetHeight || 0;
            const targetPosition = element.offsetTop - headerHeight - 20;
            
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    }

    /**
     * Switch language
     */
    function switchLanguage(lang) {
        currentLanguage = lang;
        
        // Update language buttons
        const langButtons = document.querySelectorAll('.kyriakis-lang-btn');
        langButtons.forEach(btn => {
            btn.classList.toggle('active', btn.textContent.toLowerCase() === lang);
        });
        
        // Trigger language change event
        window.dispatchEvent(new CustomEvent('languageChanged', { 
            detail: { language: lang } 
        }));
    }

    /**
     * Update page language
     */
    function updatePageLanguage(lang) {
        // Update document language attribute
        document.documentElement.lang = lang === 'gr' ? 'el' : 'en';
        
        // Update page title if needed
        const titleTranslations = {
            gr: 'Kyriakis Plumber - Επαγγελματικές Υδραυλικές Υπηρεσίες',
            en: 'Kyriakis Plumber - Professional Plumbing Services'
        };
        
        if (titleTranslations[lang]) {
            document.title = titleTranslations[lang];
        }
    }

    /**
     * Utility function to debounce function calls
     */
    function debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    /**
     * Utility function to throttle function calls
     */
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Make functions globally available
    window.kyriakisTheme = {
        scrollToSection,
        switchLanguage,
        showNotification,
        debounce,
        throttle
    };

    // Expose scroll function globally for backward compatibility
    window.scrollToSection = scrollToSection;
    window.switchLanguage = switchLanguage;

})();
