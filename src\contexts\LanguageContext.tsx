import React, { createContext, useContext, useState, ReactNode } from 'react';

export type Language = 'en' | 'gr';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const translations = {
  en: {
    // Header
    'nav.home': 'Home',
    'nav.services': 'Services',
    'nav.about': 'About',
    'nav.contact': 'Contact',
    'header.call': 'Call',
    'header.premium': 'Premium Services 2025',
    
    // Hero
    'hero.certified': 'Professional Services since 1995',
    'hero.title1': 'Plumbing &',
    'hero.title2': 'Heating',
    'hero.title3': 'Services',
    'hero.location': 'in Greece',
    'hero.description': 'Boiler installation, service, solar systems and all plumbing works with quality guarantee and 2025 technology.',
    'hero.quality': 'quality guarantee',
    'hero.emergency': '24/7 Emergency',
    'hero.experience': '30+ Years Experience',
    'hero.certified': 'Certified',
    'hero.technology': '2025 Technology',
    'hero.quote': 'Request Quote',
    'hero.call': 'Call Now',
    'hero.available': 'Available Now - Immediate Service',
    
    // Services
    'services.badge': '2025 Services',
    'services.title': 'Our Services',
    'services.description': 'We offer comprehensive plumbing and heating services with modern materials, cutting-edge technology and quality guarantee',
    'services.boiler.title': 'Boiler Installation',
    'services.boiler.description': 'Complete boiler room installation with modern heating systems and high energy efficiency.',
    'services.boiler.badge': 'Popular',
    'services.service.title': 'Boiler Service',
    'services.service.description': 'Regular maintenance and repair of boiler rooms for optimal operation and safety.',
    'services.service.badge': 'Maintenance',
    'services.solar.title': 'Solar Systems',
    'services.solar.description': 'Installation of solar collectors for economical and ecological water heating.',
    'services.solar.badge': 'Eco-Friendly',
    'services.underfloor.title': 'Underfloor Heating',
    'services.underfloor.description': 'Modern underfloor heating systems for uniform heat distribution.',
    'services.underfloor.badge': 'Modern',
    'services.heater.title': 'Water Heater',
    'services.heater.description': 'Installation and repair of water heaters for reliable hot water supply.',
    'services.heater.badge': 'Reliable',
    'services.repair.title': 'Plumbing Repair',
    'services.repair.description': 'Professional repair of all plumbing problems with quality guarantee.',
    'services.repair.badge': '24/7',
    'services.learn': 'Learn More',
    'services.partners': 'We Partner with the Best Companies',
    
    // About
    'about.company': 'Our Company',
    'about.title1': 'About',
    'about.title2': 'Kyriakis Plumber',
    'about.description1': 'With more than 30 years of experience in plumbing and heating, Kyriakis Plumber has established itself as one of the most reliable companies in Greece.',
    'about.description2': 'We specialize in boiler installations, solar systems, underfloor heating and all plumbing works with quality guarantee and reliability.',
    'about.clients': 'Satisfied Clients',
    'about.years': 'Years Experience',
    'about.projects': 'Completed Projects',
    'about.rating': 'Customer Rating',
    'about.certified': 'Certified Company',
    'about.cert.description': 'We have all the necessary licenses and certifications to provide professional plumbing and heating services with 2025 technology.',
    'about.quote.btn': 'Request Quote',
    'about.projects.btn': 'View Our Projects',
    
    // Contact
    'contact.badge': '2025 Contact',
    'contact.title': 'Contact Us',
    'contact.description': 'Send us a message and we will contact you immediately with personalized service',
    'contact.name': 'Name',
    'contact.email': 'Email',
    'contact.phone': 'Phone',
    'contact.service': 'Service',
    'contact.message': 'Message',
    'contact.select': 'Select service',
    'contact.placeholder': 'Describe the problem or service you need...',
    'contact.send': 'Send Message',
    'contact.quote': 'Request Quote',
    'contact.emergency': 'Emergency',
    'contact.sending': 'Sending...',
    'contact.success': '✅ Your message was sent successfully!',
    'contact.error': '❌ There was a problem. Please try again.',
    'contact.phone.title': 'Phone',
    'contact.phone.available': '24/7 Available',
    'contact.email.title': 'Email',
    'contact.email.fast': 'Fast Response',
    'contact.hours.title': 'Working Hours',
    'contact.hours.schedule': 'Mon-Fri: 8:00-18:00',
    'contact.hours.emergency': '24/7 Emergency',
    
    // Footer
    'footer.premium': 'Premium Services 2025',
    'footer.description': 'Professional plumbing and heating services with more than 30 years of experience. We serve all of Greece with quality guarantee and cutting-edge technology.',
    'footer.links': 'Quick Links',
    'footer.services': 'Services',
    'footer.contact': 'Contact',
    'footer.schedule': 'Mon-Fri: 8:00-18:00',
    'footer.weekend': 'Weekend: Emergency',
    'footer.location': 'Athens, Greece',
    'footer.serve': 'We serve all of Greece',
    'footer.copyright': '© 2025 Kyriakis Plumber. All rights reserved. Designed with ❤️ for the future.',
    'footer.terms': 'Terms of Use',
    'footer.privacy': 'Privacy Policy',
    'footer.cookies': 'Cookies'
  },
  gr: {
    // Header
    'nav.home': 'Αρχική',
    'nav.services': 'Υπηρεσίες',
    'nav.about': 'Σχετικά',
    'nav.contact': 'Επικοινωνία',
    'header.call': 'Κλήση',
    'header.premium': 'Premium Services 2025',
    
    // Hero
    'hero.certified': 'Επαγγελματικές Υπηρεσίες από το 1995',
    'hero.title1': 'Θερμοϋδραυλικές',
    'hero.title2': 'Υπηρεσίες',
    'hero.title3': '',
    'hero.location': 'στην Ελλάδα',
    'hero.description': 'Εγκατάσταση λεβητοστασίου, service, ηλιακά συστήματα και όλες οι θερμοϋδραυλικές εργασίες με εγγύηση ποιότητας και τεχνολογία 2025.',
    'hero.quality': 'εγγύηση ποιότητας',
    'hero.emergency': '24/7 Επείγοντα',
    'hero.experience': '30+ Χρόνια Εμπειρίας',
    'hero.certified': 'Πιστοποιημένοι',
    'hero.technology': 'Τεχνολογία 2025',
    'hero.quote': 'Ζητήστε Προσφορά',
    'hero.call': 'Κλήση Τώρα',
    'hero.available': 'Διαθέσιμοι Τώρα - Άμεση Εξυπηρέτηση',
    
    // Services
    'services.badge': 'Υπηρεσίες 2025',
    'services.title': 'Οι Υπηρεσίες Μας',
    'services.description': 'Προσφέρουμε ολοκληρωμένες θερμοϋδραυλικές υπηρεσίες με σύγχρονα υλικά, τεχνολογία αιχμής και εγγύηση ποιότητας',
    'services.boiler.title': 'Εγκατάσταση Λεβητοστασίου',
    'services.boiler.description': 'Πλήρης εγκατάσταση λεβητοστασίου με σύγχρονα συστήματα θέρμανσης και υψηλή ενεργειακή απόδοση.',
    'services.boiler.badge': 'Δημοφιλές',
    'services.service.title': 'Service Λεβητοστασίου',
    'services.service.description': 'Τακτική συντήρηση και επισκευή λεβητοστασίων για βέλτιστη λειτουργία και ασφάλεια.',
    'services.service.badge': 'Συντήρηση',
    'services.solar.title': 'Ηλιακά Συστήματα',
    'services.solar.description': 'Εγκατάσταση ηλιακών συλλεκτών για οικονομική και οικολογική θέρμανση νερού.',
    'services.solar.badge': 'Οικολογικό',
    'services.underfloor.title': 'Ενδοδαπέδια Θέρμανση',
    'services.underfloor.description': 'Σύγχρονα συστήματα ενδοδαπέδιας θέρμανσης για ομοιόμορφη κατανομή θερμότητας.',
    'services.underfloor.badge': 'Σύγχρονο',
    'services.heater.title': 'Θερμοσίφωνο',
    'services.heater.description': 'Εγκατάσταση και επισκευή θερμοσιφώνων για αξιόπιστη παροχή ζεστού νερού.',
    'services.heater.badge': 'Αξιόπιστο',
    'services.repair.title': 'Επισκευή Υδραυλικών',
    'services.repair.description': 'Επαγγελματική επισκευή όλων των υδραυλικών προβλημάτων με εγγύηση ποιότητας.',
    'services.repair.badge': '24/7',
    'services.learn': 'Μάθετε Περισσότερα',
    'services.partners': 'Συνεργαζόμαστε με τις Καλύτερες Εταιρείες',
    
    // About
    'about.company': 'Η Εταιρεία μας',
    'about.title1': 'Σχετικά με την',
    'about.title2': 'Kyriakis Plumber',
    'about.description1': 'Με περισσότερα από 30 χρόνια εμπειρίας στον χώρο των θερμοϋδραυλικών, η Kyriakis Plumber έχει εδραιωθεί ως μία από τις πιο αξιόπιστες εταιρείες στην Ελλάδα.',
    'about.description2': 'Εξειδικευόμαστε σε εγκαταστάσεις λεβητοστασίων, ηλιακών συστημάτων, ενδοδαπέδιας θέρμανσης και όλων των θερμοϋδραυλικών εργασιών με εγγύηση ποιότητας και αξιοπιστίας.',
    'about.clients': 'Ικανοποιημένοι Πελάτες',
    'about.years': 'Χρόνια Εμπειρίας',
    'about.projects': 'Ολοκληρωμένα Έργα',
    'about.rating': 'Αξιολόγηση Πελατών',
    'about.certified': 'Πιστοποιημένη Εταιρεία',
    'about.cert.description': 'Διαθέτουμε όλες τις απαραίτητες άδειες και πιστοποιήσεις για την παροχή επαγγελματικών θερμοϋδραυλικών υπηρεσιών με τεχνολογία 2025.',
    'about.quote.btn': 'Ζητήστε Προσφορά',
    'about.projects.btn': 'Δείτε τα Έργα μας',
    
    // Contact
    'contact.badge': 'Επικοινωνία 2025',
    'contact.title': 'Επικοινωνήστε Μαζί Μας',
    'contact.description': 'Στείλτε μας μήνυμα και θα επικοινωνήσουμε άμεσα μαζί σας με προσωπική εξυπηρέτηση',
    'contact.name': 'Όνομα',
    'contact.email': 'Email',
    'contact.phone': 'Τηλέφωνο',
    'contact.service': 'Υπηρεσία',
    'contact.message': 'Μήνυμα',
    'contact.select': 'Επιλέξτε υπηρεσία',
    'contact.placeholder': 'Περιγράψτε το πρόβλημα ή την υπηρεσία που χρειάζεστε...',
    'contact.send': 'Στείλτε Μήνυμα',
    'contact.quote': 'Ζητήστε Προσφορά',
    'contact.emergency': 'Επείγον',
    'contact.sending': 'Αποστολή...',
    'contact.success': '✅ Το μήνυμά σας στάλθηκε επιτυχώς!',
    'contact.error': '❌ Υπήρξε πρόβλημα. Παρακαλώ δοκιμάστε ξανά.',
    'contact.phone.title': 'Τηλέφωνο',
    'contact.phone.available': '24/7 Διαθέσιμο',
    'contact.email.title': 'Email',
    'contact.email.fast': 'Γρήγορη Απάντηση',
    'contact.hours.title': 'Ώρες Λειτουργίας',
    'contact.hours.schedule': 'Δευτ-Παρ: 8:00-18:00',
    'contact.hours.emergency': '24/7 Επείγοντα',
    
    // Footer
    'footer.premium': 'Premium Services 2025',
    'footer.description': 'Επαγγελματικές θερμοϋδραυλικές υπηρεσίες με περισσότερα από 30 χρόνια εμπειρίας. Εξυπηρετούμε όλη την Ελλάδα με εγγύηση ποιότητας και τεχνολογία αιχμής.',
    'footer.links': 'Γρήγοροι Σύνδεσμοι',
    'footer.services': 'Υπηρεσίες',
    'footer.contact': 'Επικοινωνία',
    'footer.schedule': 'Δευτ-Παρ: 8:00-18:00',
    'footer.weekend': 'Σαββατοκύριακο: Επείγοντα',
    'footer.location': 'Αθήνα, Ελλάδα',
    'footer.serve': 'Εξυπηρετούμε όλη την Ελλάδα',
    'footer.copyright': '© 2025 Kyriakis Plumber. Όλα τα δικαιώματα διατηρούνται. Designed with ❤️ for the future.',
    'footer.terms': 'Όροι Χρήσης',
    'footer.privacy': 'Πολιτική Απορρήτου',
    'footer.cookies': 'Cookies'
  }
};

export const LanguageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<Language>('gr');

  const t = (key: string): string => {
    return translations[language][key] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};