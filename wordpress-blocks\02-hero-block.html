<!-- WordPress Gutenberg Block: Hero Section -->
<!-- Copy this entire block into a Custom HTML block in WordPress -->

<style>
.kyriakis-hero {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #312e81 100%);
  color: white;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.kyriakis-hero::before {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.1), transparent 70%);
}

.kyriakis-hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.kyriakis-hero-bg-circle {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
}

.kyriakis-hero-bg-circle:nth-child(1) {
  top: 5rem;
  left: 5rem;
  width: 18rem;
  height: 18rem;
  background: rgba(59, 130, 246, 0.1);
  animation: pulse 4s ease-in-out infinite;
}

.kyriakis-hero-bg-circle:nth-child(2) {
  bottom: 5rem;
  right: 5rem;
  width: 24rem;
  height: 24rem;
  background: rgba(147, 51, 234, 0.1);
  animation: pulse 4s ease-in-out infinite 1s;
}

.kyriakis-hero-bg-circle:nth-child(3) {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16rem;
  height: 16rem;
  background: rgba(6, 182, 212, 0.1);
  animation: pulse 4s ease-in-out infinite 0.5s;
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.1); }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.kyriakis-hero-container {
  position: relative;
  max-width: 1280px;
  margin: 0 auto;
  padding: 8rem 1rem 5rem;
  z-index: 10;
}

.kyriakis-hero-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 80vh;
}

@media (min-width: 1024px) {
  .kyriakis-hero-content {
    grid-template-columns: 1fr 1fr;
  }
}

.kyriakis-hero-text {
  opacity: 0;
  transform: translateY(40px);
  animation: fadeInUp 1s ease-out forwards;
}

.kyriakis-hero-badge {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #93c5fd;
  margin-bottom: 1.5rem;
}

.kyriakis-hero-badge-icon {
  background: linear-gradient(45deg, #3b82f6, #06b6d4);
  padding: 0.5rem;
  border-radius: 0.75rem;
}

.kyriakis-hero-badge-text {
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.kyriakis-hero-sparkle {
  width: 1rem;
  height: 1rem;
  color: #fbbf24;
  animation: pulse 2s ease-in-out infinite;
}

.kyriakis-hero-title {
  font-size: 3rem;
  font-weight: bold;
  line-height: 1.1;
  margin-bottom: 2rem;
}

@media (min-width: 1024px) {
  .kyriakis-hero-title {
    font-size: 4.5rem;
  }
}

.kyriakis-hero-title-line1 {
  background: linear-gradient(45deg, #ffffff, #dbeafe);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.kyriakis-hero-title-line2 {
  background: linear-gradient(45deg, #60a5fa, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.kyriakis-hero-location {
  font-size: 1.5rem;
  font-weight: normal;
  color: #dbeafe;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 1rem;
}

@media (min-width: 1024px) {
  .kyriakis-hero-location {
    font-size: 2.25rem;
  }
}

.kyriakis-hero-zap {
  width: 2rem;
  height: 2rem;
  color: #fbbf24;
  animation: bounce 2s ease-in-out infinite;
}

.kyriakis-hero-description {
  font-size: 1.25rem;
  color: #dbeafe;
  max-width: 32rem;
  line-height: 1.6;
  margin-bottom: 2rem;
}

@media (min-width: 1024px) {
  .kyriakis-hero-description {
    font-size: 1.5rem;
  }
}

.kyriakis-hero-quality {
  color: #fbbf24;
  font-weight: 600;
}

.kyriakis-hero-features {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

@media (min-width: 640px) {
  .kyriakis-hero-features {
    grid-template-columns: 1fr 1fr;
  }
}

.kyriakis-hero-feature {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 1rem;
  padding: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.kyriakis-hero-feature:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.kyriakis-hero-feature-icon {
  padding: 0.75rem;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.kyriakis-hero-feature:hover .kyriakis-hero-feature-icon {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.kyriakis-hero-feature-text {
  font-weight: 600;
  font-size: 1.125rem;
}

.kyriakis-hero-buttons {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding-top: 1rem;
}

.kyriakis-hero-buttons-row {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 640px) {
  .kyriakis-hero-buttons-row {
    flex-direction: row;
  }
}

.kyriakis-hero-btn-primary {
  background: linear-gradient(45deg, #2563eb, #9333ea);
  color: white;
  padding: 1.25rem 2.5rem;
  border-radius: 1rem;
  font-weight: bold;
  font-size: 1.125rem;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: none;
  cursor: pointer;
}

.kyriakis-hero-btn-primary:hover {
  background: linear-gradient(45deg, #1d4ed8, #7c3aed);
  transform: scale(1.05);
  box-shadow: 0 25px 50px -12px rgba(59, 130, 246, 0.25);
  color: white;
  text-decoration: none;
}

.kyriakis-hero-btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  color: white;
  padding: 1.25rem 2.5rem;
  border-radius: 1rem;
  font-weight: bold;
  font-size: 1.125rem;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.kyriakis-hero-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.05);
  color: white;
  text-decoration: none;
}

.kyriakis-hero-whatsapp {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.kyriakis-hero-whatsapp-btn {
  background: linear-gradient(45deg, #10b981, #059669);
  color: white;
  padding: 1rem 2rem;
  border-radius: 1rem;
  font-weight: bold;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.kyriakis-hero-whatsapp-btn:hover {
  background: linear-gradient(45deg, #059669, #047857);
  transform: scale(1.05);
  color: white;
  text-decoration: none;
}

.kyriakis-hero-visual {
  opacity: 0;
  transform: translateY(40px);
  animation: fadeInUp 1s ease-out 0.3s forwards;
}

.kyriakis-hero-dashboard {
  position: relative;
}

.kyriakis-hero-dashboard-main {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(40px);
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.kyriakis-hero-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.kyriakis-hero-stat {
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.kyriakis-hero-stat:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.kyriakis-hero-stat-emoji {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.kyriakis-hero-stat-number {
  font-size: 1.875rem;
  font-weight: bold;
  color: white;
  margin-bottom: 0.25rem;
}

.kyriakis-hero-stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.kyriakis-hero-status {
  margin-top: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  background: rgba(16, 185, 129, 0.2);
  border-radius: 1rem;
  padding: 1rem;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.kyriakis-hero-status-dot {
  width: 0.75rem;
  height: 0.75rem;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.kyriakis-hero-status-text {
  color: #6ee7b7;
  font-weight: 600;
}

.kyriakis-hero-floating {
  position: absolute;
}

.kyriakis-hero-floating-1 {
  top: -1rem;
  right: -1rem;
  background: linear-gradient(45deg, #fbbf24, #f97316);
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  animation: bounce 3s ease-in-out infinite;
}

.kyriakis-hero-floating-2 {
  bottom: -1rem;
  left: -1rem;
  background: linear-gradient(45deg, #06b6d4, #3b82f6);
  border-radius: 1rem;
  padding: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  animation: pulse 3s ease-in-out infinite;
}

.kyriakis-hero-scroll {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s ease-in-out infinite;
}

.kyriakis-hero-scroll-indicator {
  width: 1.5rem;
  height: 2.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 1.25rem;
  display: flex;
  justify-content: center;
}

.kyriakis-hero-scroll-dot {
  width: 0.25rem;
  height: 0.75rem;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 0.125rem;
  margin-top: 0.5rem;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Icons */
.kyriakis-icon {
  width: 1.5rem;
  height: 1.5rem;
  fill: currentColor;
}

.kyriakis-icon-lg {
  width: 3rem;
  height: 3rem;
  fill: currentColor;
}
</style>

<section id="home" class="kyriakis-hero">
  <!-- Animated Background -->
  <div class="kyriakis-hero-bg">
    <div class="kyriakis-hero-bg-circle"></div>
    <div class="kyriakis-hero-bg-circle"></div>
    <div class="kyriakis-hero-bg-circle"></div>
  </div>
  
  <div class="kyriakis-hero-container">
    <div class="kyriakis-hero-content">
      <!-- Content -->
      <div class="kyriakis-hero-text">
        <div class="kyriakis-hero-badge">
          <div class="kyriakis-hero-badge-icon">
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <span class="kyriakis-hero-badge-text" id="hero-certified">Επαγγελματικές Υπηρεσίες από το 1995</span>
          <svg class="kyriakis-hero-sparkle" viewBox="0 0 24 24" fill="currentColor">
            <path d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z"/>
          </svg>
        </div>
        
        <h1 class="kyriakis-hero-title">
          <span class="kyriakis-hero-title-line1" id="hero-title1">Θερμοϋδραυλικές</span><br>
          <span class="kyriakis-hero-title-line2" id="hero-title2">Υπηρεσίες</span>
          <div class="kyriakis-hero-location">
            <span id="hero-location">στην Ελλάδα</span>
            <svg class="kyriakis-hero-zap" viewBox="0 0 24 24" fill="currentColor">
              <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
            </svg>
          </div>
        </h1>
        
        <p class="kyriakis-hero-description" id="hero-description">
          Εγκατάσταση λεβητοστασίου, service, ηλιακά συστήματα και όλες οι θερμοϋδραυλικές εργασίες με <span class="kyriakis-hero-quality">εγγύηση ποιότητας</span> και τεχνολογία 2025.
        </p>

        <!-- Features -->
        <div class="kyriakis-hero-features">
          <div class="kyriakis-hero-feature">
            <div class="kyriakis-hero-feature-icon" style="background: linear-gradient(45deg, #10b981, #059669);">
              <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12,6 12,12 16,14"/>
              </svg>
            </div>
            <span class="kyriakis-hero-feature-text" id="hero-emergency">24/7 Επείγοντα</span>
          </div>
          
          <div class="kyriakis-hero-feature">
            <div class="kyriakis-hero-feature-icon" style="background: linear-gradient(45deg, #fbbf24, #f97316);">
              <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
              </svg>
            </div>
            <span class="kyriakis-hero-feature-text" id="hero-experience">30+ Χρόνια Εμπειρίας</span>
          </div>
          
          <div class="kyriakis-hero-feature">
            <div class="kyriakis-hero-feature-icon" style="background: linear-gradient(45deg, #3b82f6, #06b6d4);">
              <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <span class="kyriakis-hero-feature-text" id="hero-certified-feature">Πιστοποιημένοι</span>
          </div>
          
          <div class="kyriakis-hero-feature">
            <div class="kyriakis-hero-feature-icon" style="background: linear-gradient(45deg, #9333ea, #ec4899);">
              <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"/>
              </svg>
            </div>
            <span class="kyriakis-hero-feature-text" id="hero-technology">Τεχνολογία 2025</span>
          </div>
        </div>

        <!-- CTA Buttons -->
        <div class="kyriakis-hero-buttons">
          <div class="kyriakis-hero-buttons-row">
            <button class="kyriakis-hero-btn-primary" onclick="scrollToSection('contact')">
              <span id="hero-quote">Ζητήστε Προσφορά</span>
              <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="5" y1="12" x2="19" y2="12"/>
                <polyline points="12,5 19,12 12,19"/>
              </svg>
            </button>
            <a href="tel:+306985814213" class="kyriakis-hero-btn-secondary">
              <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
              </svg>
              <span id="hero-call">Κλήση Τώρα</span>
            </a>
          </div>
          
          <!-- WhatsApp Button -->
          <div class="kyriakis-hero-whatsapp">
            <a href="https://wa.me/306985814213" target="_blank" class="kyriakis-hero-whatsapp-btn">
              <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"/>
              </svg>
              WhatsApp
            </a>
          </div>
        </div>
      </div>

      <!-- Visual Dashboard -->
      <div class="kyriakis-hero-visual">
        <div class="kyriakis-hero-dashboard">
          <!-- Main Dashboard -->
          <div class="kyriakis-hero-dashboard-main">
            <div class="kyriakis-hero-stats">
              <div class="kyriakis-hero-stat" style="background: linear-gradient(135deg, #3b82f6, #06b6d4);">
                <div class="kyriakis-hero-stat-emoji">👥</div>
                <div class="kyriakis-hero-stat-number">1000+</div>
                <div class="kyriakis-hero-stat-label" id="hero-clients">Ικανοποιημένοι Πελάτες</div>
              </div>
              
              <div class="kyriakis-hero-stat" style="background: linear-gradient(135deg, #10b981, #059669);">
                <div class="kyriakis-hero-stat-emoji">🕒</div>
                <div class="kyriakis-hero-stat-number">24/7</div>
                <div class="kyriakis-hero-stat-label">Διαθεσιμότητα</div>
              </div>
              
              <div class="kyriakis-hero-stat" style="background: linear-gradient(135deg, #fbbf24, #f97316);">
                <div class="kyriakis-hero-stat-emoji">⭐</div>
                <div class="kyriakis-hero-stat-number">30+</div>
                <div class="kyriakis-hero-stat-label" id="hero-years">Χρόνια Εμπειρίας</div>
              </div>
              
              <div class="kyriakis-hero-stat" style="background: linear-gradient(135deg, #9333ea, #ec4899);">
                <div class="kyriakis-hero-stat-emoji">🛡️</div>
                <div class="kyriakis-hero-stat-number">100%</div>
                <div class="kyriakis-hero-stat-label">Εγγύηση</div>
              </div>
            </div>
            
            <!-- Live Status Indicator -->
            <div class="kyriakis-hero-status">
              <div class="kyriakis-hero-status-dot"></div>
              <span class="kyriakis-hero-status-text" id="hero-available">Διαθέσιμοι Τώρα - Άμεση Εξυπηρέτηση</span>
            </div>
          </div>

          <!-- Floating Elements -->
          <div class="kyriakis-hero-floating kyriakis-hero-floating-1">
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z"/>
            </svg>
          </div>
          <div class="kyriakis-hero-floating kyriakis-hero-floating-2">
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Scroll Indicator -->
  <div class="kyriakis-hero-scroll">
    <div class="kyriakis-hero-scroll-indicator">
      <div class="kyriakis-hero-scroll-dot"></div>
    </div>
  </div>
</section>

<script>
// Hero translations
const heroTranslations = {
  gr: {
    'hero-certified': 'Επαγγελματικές Υπηρεσίες από το 1995',
    'hero-title1': 'Θερμοϋδραυλικές',
    'hero-title2': 'Υπηρεσίες',
    'hero-location': 'στην Ελλάδα',
    'hero-description': 'Εγκατάσταση λεβητοστασίου, service, ηλιακά συστήματα και όλες οι θερμοϋδραυλικές εργασίες με εγγύηση ποιότητας και τεχνολογία 2025.',
    'hero-emergency': '24/7 Επείγοντα',
    'hero-experience': '30+ Χρόνια Εμπειρίας',
    'hero-certified-feature': 'Πιστοποιημένοι',
    'hero-technology': 'Τεχνολογία 2025',
    'hero-quote': 'Ζητήστε Προσφορά',
    'hero-call': 'Κλήση Τώρα',
    'hero-clients': 'Ικανοποιημένοι Πελάτες',
    'hero-years': 'Χρόνια Εμπειρίας',
    'hero-available': 'Διαθέσιμοι Τώρα - Άμεση Εξυπηρέτηση'
  },
  en: {
    'hero-certified': 'Professional Services since 1995',
    'hero-title1': 'Plumbing &',
    'hero-title2': 'Heating Services',
    'hero-location': 'in Greece',
    'hero-description': 'Boiler installation, service, solar systems and all plumbing works with quality guarantee and 2025 technology.',
    'hero-emergency': '24/7 Emergency',
    'hero-experience': '30+ Years Experience',
    'hero-certified-feature': 'Certified',
    'hero-technology': '2025 Technology',
    'hero-quote': 'Request Quote',
    'hero-call': 'Call Now',
    'hero-clients': 'Satisfied Clients',
    'hero-years': 'Years Experience',
    'hero-available': 'Available Now - Immediate Service'
  }
};

// Listen for language changes
window.addEventListener('languageChanged', function(e) {
  const lang = e.detail.language;
  const translations = heroTranslations[lang];
  
  Object.keys(translations).forEach(key => {
    const element = document.getElementById(key);
    if (element) {
      element.textContent = translations[key];
    }
  });
});

// Smooth scroll function
function scrollToSection(sectionId) {
  const element = document.getElementById(sectionId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
}
</script>