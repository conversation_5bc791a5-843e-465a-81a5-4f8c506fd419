<!-- WordPress Gutenberg Block: About Section -->
<!-- Copy this entire block into a Custom HTML block in WordPress -->

<style>
.kyriakis-about {
  padding: 6rem 0;
  background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
}

.kyriakis-about-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

.kyriakis-about-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 5rem;
  align-items: center;
}

@media (min-width: 1024px) {
  .kyriakis-about-content {
    grid-template-columns: 1fr 1fr;
  }
}

.kyriakis-about-text {
  opacity: 0;
  transform: translateX(-40px);
  animation: fadeInLeft 1s ease-out forwards;
}

.kyriakis-about-text.visible {
  animation-delay: 0s;
}

.kyriakis-about-badge {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.kyriakis-about-badge-icon {
  background: linear-gradient(45deg, #2563eb, #9333ea);
  padding: 0.75rem;
  border-radius: 1rem;
}

.kyriakis-about-badge-text {
  color: #2563eb;
  font-weight: 600;
  font-size: 1.125rem;
}

.kyriakis-about-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 2rem;
}

@media (min-width: 1024px) {
  .kyriakis-about-title {
    font-size: 3.75rem;
  }
}

.kyriakis-about-title-line1 {
  background: linear-gradient(45deg, #111827, #374151);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.kyriakis-about-title-line2 {
  background: linear-gradient(45deg, #2563eb, #9333ea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: block;
}

.kyriakis-about-description {
  margin-bottom: 2rem;
}

.kyriakis-about-description p {
  font-size: 1.125rem;
  color: #4b5563;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.kyriakis-about-highlight {
  font-weight: bold;
  color: #2563eb;
}

.kyriakis-about-highlight-purple {
  font-weight: bold;
  color: #9333ea;
}

.kyriakis-about-features {
  margin-bottom: 2rem;
}

.kyriakis-about-feature {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  transition: all 0.3s ease;
  border: 1px solid #f3f4f6;
  opacity: 0;
  transform: translateX(40px);
  animation: fadeInRight 1s ease-out forwards;
}

.kyriakis-about-feature:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.kyriakis-about-feature:nth-child(1) { animation-delay: 0.1s; }
.kyriakis-about-feature:nth-child(2) { animation-delay: 0.2s; }
.kyriakis-about-feature:nth-child(3) { animation-delay: 0.3s; }
.kyriakis-about-feature:nth-child(4) { animation-delay: 0.4s; }
.kyriakis-about-feature:nth-child(5) { animation-delay: 0.5s; }
.kyriakis-about-feature:nth-child(6) { animation-delay: 0.6s; }

.kyriakis-about-feature-icon {
  background: linear-gradient(45deg, #3b82f6, #9333ea);
  padding: 0.75rem;
  border-radius: 0.75rem;
}

.kyriakis-about-feature-text {
  color: #374151;
  font-weight: 500;
}

.kyriakis-about-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-top: 1rem;
}

@media (min-width: 640px) {
  .kyriakis-about-buttons {
    flex-direction: row;
  }
}

.kyriakis-about-btn-primary {
  background: linear-gradient(45deg, #2563eb, #9333ea);
  color: white;
  padding: 1rem 2rem;
  border-radius: 1rem;
  font-weight: bold;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  border: none;
  cursor: pointer;
  text-align: center;
}

.kyriakis-about-btn-primary:hover {
  background: linear-gradient(45deg, #1d4ed8, #7c3aed);
  transform: scale(1.05);
  color: white;
  text-decoration: none;
}

.kyriakis-about-btn-secondary {
  border: 2px solid #2563eb;
  color: #2563eb;
  padding: 1rem 2rem;
  border-radius: 1rem;
  font-weight: bold;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  background: transparent;
  cursor: pointer;
  text-align: center;
}

.kyriakis-about-btn-secondary:hover {
  background: #2563eb;
  color: white;
  transform: scale(1.05);
  text-decoration: none;
}

.kyriakis-about-visual {
  opacity: 0;
  transform: translateX(40px);
  animation: fadeInRight 1s ease-out 0.3s forwards;
}

.kyriakis-about-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.kyriakis-about-stat {
  background: white;
  border-radius: 1.5rem;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
  border: 1px solid #f3f4f6;
}

.kyriakis-about-stat:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: translateY(-8px);
}

.kyriakis-about-stat-icon {
  padding: 1rem;
  border-radius: 1rem;
  margin-bottom: 1rem;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.kyriakis-about-stat:hover .kyriakis-about-stat-icon {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.kyriakis-about-stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  background: linear-gradient(45deg, #111827, #374151);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.kyriakis-about-stat-label {
  font-size: 0.875rem;
  color: #4b5563;
  font-weight: 500;
}

.kyriakis-about-certification {
  position: relative;
}

.kyriakis-about-cert-card {
  background: linear-gradient(135deg, #2563eb 0%, #9333ea 50%, #4338ca 100%);
  border-radius: 1.5rem;
  padding: 2rem;
  color: white;
  text-align: center;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.kyriakis-about-cert-card::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2));
  border-radius: 1.5rem;
  filter: blur(40px);
}

.kyriakis-about-cert-content {
  position: relative;
  z-index: 10;
}

.kyriakis-about-cert-icon {
  background: rgba(255, 255, 255, 0.2);
  padding: 1rem;
  border-radius: 1rem;
  width: fit-content;
  margin: 0 auto 1.5rem;
}

.kyriakis-about-cert-title {
  font-size: 1.875rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.kyriakis-about-cert-description {
  color: #dbeafe;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.kyriakis-about-cert-badges {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.75rem;
  font-size: 0.875rem;
}

.kyriakis-about-cert-badge {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.kyriakis-about-cert-badge:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Icons */
.kyriakis-icon {
  width: 1.5rem;
  height: 1.5rem;
  fill: currentColor;
}

.kyriakis-icon-lg {
  width: 2rem;
  height: 2rem;
  fill: currentColor;
}

.kyriakis-icon-xl {
  width: 3rem;
  height: 3rem;
  fill: currentColor;
}

/* Animations */
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Counter animation */
.kyriakis-counter {
  display: inline-block;
}
</style>

<section id="about" class="kyriakis-about">
  <div class="kyriakis-about-container">
    <div class="kyriakis-about-content">
      <!-- Content -->
      <div class="kyriakis-about-text">
        <div class="kyriakis-about-badge">
          <div class="kyriakis-about-badge-icon">
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <span class="kyriakis-about-badge-text" id="about-company">Η Εταιρεία μας</span>
        </div>
        
        <h2 class="kyriakis-about-title">
          <span class="kyriakis-about-title-line1" id="about-title1">Σχετικά με την</span>
          <span class="kyriakis-about-title-line2" id="about-title2">Kyriakis Plumber</span>
        </h2>
        
        <div class="kyriakis-about-description">
          <p id="about-description1">
            Με περισσότερα από <span class="kyriakis-about-highlight">30 χρόνια εμπειρίας</span> στον χώρο των θερμοϋδραυλικών, η Kyriakis Plumber έχει εδραιωθεί ως μία από τις πιο αξιόπιστες εταιρείες στην Ελλάδα.
          </p>
          <p id="about-description2">
            Εξειδικευόμαστε σε εγκαταστάσεις λεβητοστασίων, ηλιακών συστημάτων, ενδοδαπέδιας θέρμανσης και όλων των θερμοϋδραυλικών εργασιών με <span class="kyriakis-about-highlight-purple">εγγύηση ποιότητας</span> και αξιοπιστίας.
          </p>
        </div>

        <!-- Features -->
        <div class="kyriakis-about-features">
          <div class="kyriakis-about-feature">
            <div class="kyriakis-about-feature-icon">
              <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"/>
                <path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"/>
                <path d="M4 22h16"/>
                <path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"/>
                <path d="M14 14.66V17c0 .*********** 1.21C16.15 18.75 17 20.24 17 22"/>
                <path d="M18 2H6v7a6 6 0 0 0 12 0V2Z"/>
              </svg>
            </div>
            <span class="kyriakis-about-feature-text">Πιστοποιημένοι τεχνικοί με πολυετή εμπειρία</span>
          </div>
          
          <div class="kyriakis-about-feature">
            <div class="kyriakis-about-feature-icon">
              <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
              </svg>
            </div>
            <span class="kyriakis-about-feature-text">Χρήση σύγχρονων υλικών και τεχνολογιών 2025</span>
          </div>
          
          <div class="kyriakis-about-feature">
            <div class="kyriakis-about-feature-icon">
              <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <span class="kyriakis-about-feature-text">Εγγύηση ποιότητας σε όλες τις εργασίες</span>
          </div>
          
          <div class="kyriakis-about-feature">
            <div class="kyriakis-about-feature-icon">
              <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12,6 12,12 16,14"/>
              </svg>
            </div>
            <span class="kyriakis-about-feature-text">24/7 υποστήριξη για επείγοντα περιστατικά</span>
          </div>
          
          <div class="kyriakis-about-feature">
            <div class="kyriakis-about-feature-icon">
              <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <path d="M16 12l-4-4-4 4"/>
                <path d="M12 16V8"/>
              </svg>
            </div>
            <span class="kyriakis-about-feature-text">Δωρεάν εκτίμηση και προσφορές</span>
          </div>
          
          <div class="kyriakis-about-feature">
            <div class="kyriakis-about-feature-icon">
              <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <span class="kyriakis-about-feature-text">Ανταγωνιστικές τιμές χωρίς κρυφά κόστη</span>
          </div>
        </div>

        <!-- CTA Buttons -->
        <div class="kyriakis-about-buttons">
          <button class="kyriakis-about-btn-primary" onclick="scrollToSection('contact')" id="about-quote-btn">
            Ζητήστε Προσφορά
          </button>
          <button class="kyriakis-about-btn-secondary" onclick="scrollToSection('services')" id="about-projects-btn">
            Δείτε τα Έργα μας
          </button>
        </div>
      </div>

      <!-- Stats & Visual -->
      <div class="kyriakis-about-visual">
        <!-- Stats Grid -->
        <div class="kyriakis-about-stats">
          <div class="kyriakis-about-stat">
            <div class="kyriakis-about-stat-icon" style="background: linear-gradient(45deg, #3b82f6, #06b6d4);">
              <svg class="kyriakis-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                <circle cx="9" cy="7" r="4"/>
                <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
              </svg>
            </div>
            <div class="kyriakis-about-stat-number">
              <span class="kyriakis-counter" data-target="1000">0</span>+
            </div>
            <div class="kyriakis-about-stat-label" id="about-clients">Ικανοποιημένοι Πελάτες</div>
          </div>
          
          <div class="kyriakis-about-stat">
            <div class="kyriakis-about-stat-icon" style="background: linear-gradient(45deg, #10b981, #059669);">
              <svg class="kyriakis-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12,6 12,12 16,14"/>
              </svg>
            </div>
            <div class="kyriakis-about-stat-number">
              <span class="kyriakis-counter" data-target="30">0</span>+
            </div>
            <div class="kyriakis-about-stat-label" id="about-years">Χρόνια Εμπειρίας</div>
          </div>
          
          <div class="kyriakis-about-stat">
            <div class="kyriakis-about-stat-icon" style="background: linear-gradient(45deg, #9333ea, #ec4899);">
              <svg class="kyriakis-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"/>
                <line x1="3" y1="6" x2="21" y2="6"/>
                <path d="M16 10a4 4 0 0 1-8 0"/>
              </svg>
            </div>
            <div class="kyriakis-about-stat-number">
              <span class="kyriakis-counter" data-target="500">0</span>+
            </div>
            <div class="kyriakis-about-stat-label" id="about-projects">Ολοκληρωμένα Έργα</div>
          </div>
          
          <div class="kyriakis-about-stat">
            <div class="kyriakis-about-stat-icon" style="background: linear-gradient(45deg, #fbbf24, #f97316);">
              <svg class="kyriakis-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
              </svg>
            </div>
            <div class="kyriakis-about-stat-number">
              <span class="kyriakis-counter" data-target="5">0</span>★
            </div>
            <div class="kyriakis-about-stat-label" id="about-rating">Αξιολόγηση Πελατών</div>
          </div>
        </div>

        <!-- Certification Badge -->
        <div class="kyriakis-about-certification">
          <div class="kyriakis-about-cert-card">
            <div class="kyriakis-about-cert-content">
              <div class="kyriakis-about-cert-icon">
                <svg class="kyriakis-icon-xl" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 class="kyriakis-about-cert-title" id="about-certified">Πιστοποιημένη Εταιρεία</h3>
              <p class="kyriakis-about-cert-description" id="about-cert-description">
                Διαθέτουμε όλες τις απαραίτητες άδειες και πιστοποιήσεις για την παροχή επαγγελματικών θερμοϋδραυλικών υπηρεσιών με τεχνολογία 2025.
              </p>
              <div class="kyriakis-about-cert-badges">
                <span class="kyriakis-about-cert-badge">ISO 9001</span>
                <span class="kyriakis-about-cert-badge">Άδεια Εγκατάστασης</span>
                <span class="kyriakis-about-cert-badge">Ασφάλεια Εργασίας</span>
                <span class="kyriakis-about-cert-badge">Πιστοποίηση 2025</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
// About translations
const aboutTranslations = {
  gr: {
    'about-company': 'Η Εταιρεία μας',
    'about-title1': 'Σχετικά με την',
    'about-title2': 'Kyriakis Plumber',
    'about-description1': 'Με περισσότερα από 30 χρόνια εμπειρίας στον χώρο των θερμοϋδραυλικών, η Kyriakis Plumber έχει εδραιωθεί ως μία από τις πιο αξιόπιστες εταιρείες στην Ελλάδα.',
    'about-description2': 'Εξειδικευόμαστε σε εγκαταστάσεις λεβητοστασίων, ηλιακών συστημάτων, ενδοδαπέδιας θέρμανσης και όλων των θερμοϋδραυλικών εργασιών με εγγύηση ποιότητας και αξιοπιστίας.',
    'about-clients': 'Ικανοποιημένοι Πελάτες',
    'about-years': 'Χρόνια Εμπειρίας',
    'about-projects': 'Ολοκληρωμένα Έργα',
    'about-rating': 'Αξιολόγηση Πελατών',
    'about-certified': 'Πιστοποιημένη Εταιρεία',
    'about-cert-description': 'Διαθέτουμε όλες τις απαραίτητες άδειες και πιστοποιήσεις για την παροχή επαγγελματικών θερμοϋδραυλικών υπηρεσιών με τεχνολογία 2025.',
    'about-quote-btn': 'Ζητήστε Προσφορά',
    'about-projects-btn': 'Δείτε τα Έργα μας'
  },
  en: {
    'about-company': 'Our Company',
    'about-title1': 'About',
    'about-title2': 'Kyriakis Plumber',
    'about-description1': 'With more than 30 years of experience in plumbing and heating, Kyriakis Plumber has established itself as one of the most reliable companies in Greece.',
    'about-description2': 'We specialize in boiler installations, solar systems, underfloor heating and all plumbing works with quality guarantee and reliability.',
    'about-clients': 'Satisfied Clients',
    'about-years': 'Years Experience',
    'about-projects': 'Completed Projects',
    'about-rating': 'Customer Rating',
    'about-certified': 'Certified Company',
    'about-cert-description': 'We have all the necessary licenses and certifications to provide professional plumbing and heating services with 2025 technology.',
    'about-quote-btn': 'Request Quote',
    'about-projects-btn': 'View Our Projects'
  }
};

// Counter animation
function animateCounter(element, target, duration = 2000) {
  let start = 0;
  const increment = target / (duration / 16);
  const timer = setInterval(() => {
    start += increment;
    if (start >= target) {
      start = target;
      clearInterval(timer);
    }
    element.textContent = Math.floor(start);
  }, 16);
}

// Intersection Observer for counter animation
const observerOptions = {
  threshold: 0.3,
  rootMargin: '0px 0px -100px 0px'
};

const observer = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      const counters = entry.target.querySelectorAll('.kyriakis-counter');
      counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        animateCounter(counter, target);
      });
      observer.unobserve(entry.target);
    }
  });
}, observerOptions);

// Start observing when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  const aboutSection = document.getElementById('about');
  if (aboutSection) {
    observer.observe(aboutSection);
  }
});

// Listen for language changes
window.addEventListener('languageChanged', function(e) {
  const lang = e.detail.language;
  const translations = aboutTranslations[lang];
  
  Object.keys(translations).forEach(key => {
    const element = document.getElementById(key);
    if (element) {
      element.textContent = translations[key];
    }
  });
});

// Smooth scroll function
function scrollToSection(sectionId) {
  const element = document.getElementById(sectionId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
}
</script>