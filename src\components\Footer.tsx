import React from 'react';
import { Phone, Mail, MapPin, Clock, Wrench, Zap, Star, MessageCircle } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const Footer: React.FC = () => {
  const { t } = useLanguage();

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer className="bg-gradient-to-br from-gray-900 via-slate-900 to-gray-900 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-900/10 to-purple-900/10"></div>
        <div className="absolute top-20 left-20 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Company Info */}
          <div className="space-y-8">
            <div className="flex items-center space-x-4 group cursor-pointer">
              <div className="relative">
                <div className="bg-gradient-to-br from-blue-600 to-purple-600 p-4 rounded-2xl shadow-xl group-hover:shadow-2xl transition-all duration-300 group-hover:scale-110">
                  <Wrench className="w-8 h-8 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full p-1">
                  <Zap className="w-3 h-3 text-white" />
                </div>
              </div>
              <div>
                <h3 className="text-2xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                  Kyriakis Plumber
                </h3>
                <p className="text-blue-300 text-sm font-semibold">Premium Services 2025</p>
              </div>
            </div>
            
            <p className="text-gray-300 leading-relaxed">
              Επαγγελματικές θερμοϋδραυλικές υπηρεσίες με περισσότερα από 30 χρόνια εμπειρίας. 
              Εξυπηρετούμε όλη την Ελλάδα με εγγύηση ποιότητας και τεχνολογία αιχμής.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-xl font-bold mb-8 flex items-center gap-2">
              <Star className="w-5 h-5 text-yellow-400" />
              Γρήγοροι Σύνδεσμοι
            </h4>
            <ul className="space-y-4">
              {[
                { id: 'home', label: 'Αρχική' },
                { id: 'services', label: 'Υπηρεσίες' },
                { id: 'about', label: 'Σχετικά' },
                { id: 'contact', label: 'Επικοινωνία' }
              ].map((link) => (
                <li key={link.id}>
                  <button
                    onClick={() => scrollToSection(link.id)}
                    className="text-gray-300 hover:text-white transition-all duration-300 hover:translate-x-2 flex items-center gap-2 group"
                  >
                    <div className="w-2 h-2 bg-blue-500 rounded-full group-hover:bg-yellow-400 transition-colors duration-300"></div>
                    {link.label}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-xl font-bold mb-8 flex items-center gap-2">
              <Wrench className="w-5 h-5 text-blue-400" />
              Υπηρεσίες
            </h4>
            <ul className="space-y-4 text-gray-300">
              {[
                'Εγκατάσταση Λεβητοστασίου',
                'Service Λεβητοστασίου',
                'Ηλιακά Συστήματα',
                'Ενδοδαπέδια Θέρμανση',
                'Θερμοσίφωνο',
                'Επισκευή Υδραυλικών'
              ].map((service, index) => (
                <li key={index} className="hover:text-white transition-colors duration-300 cursor-pointer flex items-center gap-2 group">
                  <div className="w-1 h-1 bg-purple-500 rounded-full group-hover:bg-cyan-400 transition-colors duration-300"></div>
                  {service}
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-xl font-bold mb-8 flex items-center gap-2">
              <Phone className="w-5 h-5 text-green-400" />
              Επικοινωνία
            </h4>
            <div className="space-y-6">
              {[
                {
                  icon: Phone,
                  title: '+30 ************',
                  subtitle: '24/7 Επείγοντα',
                  color: 'from-green-500 to-emerald-500'
                },
                {
                  icon: Mail,
                  title: '<EMAIL>',
                  subtitle: 'Γρήγορη Απάντηση',
                  color: 'from-blue-500 to-cyan-500'
                },
                {
                  icon: MapPin,
                  title: 'Αθήνα, Ελλάδα',
                  subtitle: 'Εξυπηρετούμε όλη την Ελλάδα',
                  color: 'from-purple-500 to-pink-500'
                },
                {
                  icon: Clock,
                  title: 'Δευτ-Παρ: 8:00-18:00',
                  subtitle: 'Σαββατοκύριακο: Επείγοντα',
                  color: 'from-yellow-500 to-orange-500'
                }
              ].map((contact, index) => (
                <div key={index} className="flex items-start gap-4 group">
                  <div className={`bg-gradient-to-r ${contact.color} p-3 rounded-xl shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110`}>
                    <contact.icon className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <p className="text-white font-semibold">{contact.title}</p>
                    <p className="text-gray-400 text-sm">{contact.subtitle}</p>
                    {contact.icon === Phone && (
                      <div className="flex gap-2 mt-2">
                        <a
                          href="https://wa.me/306985814213"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="bg-green-500 hover:bg-green-600 text-white p-1.5 rounded-lg transition-all duration-300 hover:scale-110"
                          title="WhatsApp"
                        >
                          <MessageCircle className="w-4 h-4" />
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-16 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm mb-4 md:mb-0">
              {t('footer.copyright')}
            </p>
            <div className="flex space-x-8">
              {[t('footer.terms'), t('footer.privacy'), t('footer.cookies')].map((link, index) => (
                <a 
                  key={index}
                  href="#" 
                  className="text-gray-400 hover:text-white text-sm transition-colors duration-300 hover:underline"
                >
                  {link}
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;