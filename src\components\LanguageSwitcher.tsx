import React from 'react';
import { Globe } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const LanguageSwitcher: React.FC = () => {
  const { language, setLanguage } = useLanguage();

  return (
    <div className="flex items-center gap-2">
      <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-2 border border-white/20">
        <Globe className="w-4 h-4 text-white" />
      </div>
      <div className="flex bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 overflow-hidden">
        <button
          onClick={() => setLanguage('gr')}
          className={`px-4 py-2 text-sm font-semibold transition-all duration-300 ${
            language === 'gr'
              ? 'bg-white text-blue-600'
              : 'text-white hover:bg-white/20'
          }`}
        >
          GR
        </button>
        <button
          onClick={() => setLanguage('en')}
          className={`px-4 py-2 text-sm font-semibold transition-all duration-300 ${
            language === 'en'
              ? 'bg-white text-blue-600'
              : 'text-white hover:bg-white/20'
          }`}
        >
          EN
        </button>
      </div>
    </div>
  );
};

export default LanguageSwitcher;