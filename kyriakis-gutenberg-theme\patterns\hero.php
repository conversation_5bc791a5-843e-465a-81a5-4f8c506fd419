<?php
/**
 * Title: Hero Section
 * Slug: kyriakis/hero
 * Categories: kyriakis-patterns
 * Description: Modern hero section with animated background and call-to-action
 */
?>

<!-- wp:html -->
<section id="home" class="kyriakis-hero">
  <div class="kyriakis-hero-bg">
    <div class="kyriakis-hero-bg-circle"></div>
    <div class="kyriakis-hero-bg-circle"></div>
    <div class="kyriakis-hero-bg-circle"></div>
  </div>
  
  <div class="kyriakis-container">
    <div class="kyriakis-hero-content">
      <div class="kyriakis-hero-text">
        <div class="kyriakis-hero-badge">
          <svg class="kyriakis-icon-sm" viewBox="0 0 24 24" fill="currentColor">
            <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
          </svg>
          <span id="hero-badge">24/7 Emergency Service</span>
        </div>
        
        <h1 id="hero-title">Professional Plumbing Services in Greece</h1>
        <p id="hero-subtitle">Expert hydraulic solutions with modern techniques and premium materials. Serving residential and commercial clients with guaranteed quality work.</p>
        
        <div class="kyriakis-hero-stats">
          <div class="kyriakis-hero-stat">
            <div class="kyriakis-hero-stat-number">500+</div>
            <div class="kyriakis-hero-stat-label" id="hero-stat-1">Happy Customers</div>
          </div>
          <div class="kyriakis-hero-stat">
            <div class="kyriakis-hero-stat-number">10+</div>
            <div class="kyriakis-hero-stat-label" id="hero-stat-2">Years Experience</div>
          </div>
          <div class="kyriakis-hero-stat">
            <div class="kyriakis-hero-stat-number">24/7</div>
            <div class="kyriakis-hero-stat-label" id="hero-stat-3">Emergency Service</div>
          </div>
        </div>
        
        <div class="kyriakis-hero-buttons">
          <a href="tel:+306985814213" class="kyriakis-hero-btn kyriakis-hero-btn-primary">
            <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
            </svg>
            <span id="hero-call-btn">Call Now</span>
          </a>
          <button class="kyriakis-hero-btn kyriakis-hero-btn-secondary" onclick="scrollToSection('services')">
            <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/>
            </svg>
            <span id="hero-services-btn">Our Services</span>
          </button>
        </div>
      </div>
      
      <div class="kyriakis-hero-image">
        <div class="kyriakis-hero-image-container">
          <img src="/wp-content/uploads/Λεβητοστάσιο.jpg" alt="Professional Plumbing Services" class="kyriakis-hero-main-image">
          <div class="kyriakis-hero-image-overlay">
            <div class="kyriakis-hero-image-badge">
              <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 12l2 2 4-4"/>
                <circle cx="12" cy="12" r="10"/>
              </svg>
              <span id="hero-certified">Certified Professional</span>
            </div>
          </div>
        </div>
        
        <div class="kyriakis-hero-floating-cards">
          <div class="kyriakis-hero-card">
            <div class="kyriakis-hero-card-icon">
              <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <div class="kyriakis-hero-card-content">
              <div class="kyriakis-hero-card-title" id="hero-card-1-title">Premium Quality</div>
              <div class="kyriakis-hero-card-text" id="hero-card-1-text">Top-grade materials</div>
            </div>
          </div>
          
          <div class="kyriakis-hero-card">
            <div class="kyriakis-hero-card-icon">
              <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12,6 12,12 16,14"/>
              </svg>
            </div>
            <div class="kyriakis-hero-card-content">
              <div class="kyriakis-hero-card-title" id="hero-card-2-title">Fast Response</div>
              <div class="kyriakis-hero-card-text" id="hero-card-2-text">Same day service</div>
            </div>
          </div>
          
          <div class="kyriakis-hero-card">
            <div class="kyriakis-hero-card-icon">
              <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 12l2 2 4-4"/>
                <path d="M21 12c.552 0 1-.448 1-1V8c0-.552-.448-1-1-1h-1V6c0-2.761-2.239-5-5-5H8C5.239 1 3 3.239 3 6v1H2c-.552 0-1 .448-1 1v3c0 .552.448 1 1 1h1v1c0 2.761 2.239 5 5 5h8c2.761 0 5-2.239 5-5v-1h1z"/>
              </svg>
            </div>
            <div class="kyriakis-hero-card-content">
              <div class="kyriakis-hero-card-title" id="hero-card-3-title">Guaranteed Work</div>
              <div class="kyriakis-hero-card-text" id="hero-card-3-text">Full warranty</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="kyriakis-hero-scroll">
    <button onclick="scrollToSection('services')" class="kyriakis-hero-scroll-btn">
      <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="6,9 12,15 18,9"/>
      </svg>
    </button>
  </div>
</section>

<style>
.kyriakis-hero {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #312e81 100%);
  color: white;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.kyriakis-hero::before {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.1), transparent 70%);
}

.kyriakis-hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.kyriakis-hero-bg-circle {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
}

.kyriakis-hero-bg-circle:nth-child(1) {
  top: 5rem;
  left: 5rem;
  width: 18rem;
  height: 18rem;
  background: rgba(59, 130, 246, 0.1);
  animation: pulse 4s ease-in-out infinite;
}

.kyriakis-hero-bg-circle:nth-child(2) {
  bottom: 5rem;
  right: 5rem;
  width: 24rem;
  height: 24rem;
  background: rgba(147, 51, 234, 0.1);
  animation: pulse 6s ease-in-out infinite reverse;
}

.kyriakis-hero-bg-circle:nth-child(3) {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20rem;
  height: 20rem;
  background: rgba(16, 185, 129, 0.05);
  animation: pulse 5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

.kyriakis-hero-content {
  position: relative;
  z-index: 10;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  padding: 2rem 0;
}

@media (max-width: 1024px) {
  .kyriakis-hero-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }
}

.kyriakis-hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 2rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #fbbf24;
}

.kyriakis-hero h1 {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  background: linear-gradient(45deg, #ffffff, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (max-width: 768px) {
  .kyriakis-hero h1 {
    font-size: 2.5rem;
  }
}

.kyriakis-hero p {
  font-size: 1.25rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
}

.kyriakis-hero-stats {
  display: flex;
  gap: 2rem;
  margin-bottom: 2.5rem;
}

@media (max-width: 768px) {
  .kyriakis-hero-stats {
    justify-content: center;
  }
}

.kyriakis-hero-stat {
  text-align: center;
}

.kyriakis-hero-stat-number {
  font-size: 2rem;
  font-weight: 800;
  color: #60a5fa;
  margin-bottom: 0.25rem;
}

.kyriakis-hero-stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
}

.kyriakis-hero-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

@media (max-width: 768px) {
  .kyriakis-hero-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }
}

.kyriakis-hero-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 1rem;
}

.kyriakis-hero-btn-primary {
  background: linear-gradient(45deg, #2563eb, #9333ea);
  color: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.kyriakis-hero-btn-primary:hover {
  background: linear-gradient(45deg, #1d4ed8, #7c3aed);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
  color: white;
  text-decoration: none;
}

.kyriakis-hero-btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.kyriakis-hero-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.kyriakis-hero-image {
  position: relative;
}

.kyriakis-hero-image-container {
  position: relative;
  border-radius: 2rem;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.kyriakis-hero-main-image {
  width: 100%;
  height: 400px;
  object-fit: cover;
}

.kyriakis-hero-image-overlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
}

.kyriakis-hero-image-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 1rem;
  padding: 0.75rem 1rem;
  color: #10b981;
  font-weight: 600;
  font-size: 0.875rem;
}

.kyriakis-hero-floating-cards {
  position: absolute;
  left: -2rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (max-width: 1024px) {
  .kyriakis-hero-floating-cards {
    position: static;
    transform: none;
    flex-direction: row;
    justify-content: center;
    margin-top: 2rem;
  }
}

.kyriakis-hero-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 1rem;
  padding: 1rem;
  color: #1f2937;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  min-width: 200px;
}

.kyriakis-hero-card-icon {
  background: linear-gradient(45deg, #2563eb, #9333ea);
  color: white;
  padding: 0.5rem;
  border-radius: 0.75rem;
  flex-shrink: 0;
}

.kyriakis-hero-card-title {
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.kyriakis-hero-card-text {
  font-size: 0.75rem;
  color: #6b7280;
}

.kyriakis-hero-scroll {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
}

.kyriakis-hero-scroll-btn {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: bounce 2s infinite;
}

.kyriakis-hero-scroll-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}
</style>

<script>
// Hero section language translations
const heroTranslations = {
  gr: {
    'hero-badge': '24/7 Υπηρεσία Έκτακτης Ανάγκης',
    'hero-title': 'Επαγγελματικές Υδραυλικές Υπηρεσίες στην Ελλάδα',
    'hero-subtitle': 'Εξειδικευμένες υδραυλικές λύσεις με σύγχρονες τεχνικές και premium υλικά. Εξυπηρετούμε οικιακούς και εμπορικούς πελάτες με εγγυημένη ποιότητα εργασίας.',
    'hero-stat-1': 'Ικανοποιημένοι Πελάτες',
    'hero-stat-2': 'Χρόνια Εμπειρίας',
    'hero-stat-3': 'Υπηρεσία Έκτακτης Ανάγκης',
    'hero-call-btn': 'Κλήση Τώρα',
    'hero-services-btn': 'Οι Υπηρεσίες μας',
    'hero-certified': 'Πιστοποιημένος Επαγγελματίας',
    'hero-card-1-title': 'Premium Ποιότητα',
    'hero-card-1-text': 'Υλικά υψηλής ποιότητας',
    'hero-card-2-title': 'Γρήγορη Ανταπόκριση',
    'hero-card-2-text': 'Εξυπηρέτηση την ίδια μέρα',
    'hero-card-3-title': 'Εγγυημένη Εργασία',
    'hero-card-3-text': 'Πλήρης εγγύηση'
  },
  en: {
    'hero-badge': '24/7 Emergency Service',
    'hero-title': 'Professional Plumbing Services in Greece',
    'hero-subtitle': 'Expert hydraulic solutions with modern techniques and premium materials. Serving residential and commercial clients with guaranteed quality work.',
    'hero-stat-1': 'Happy Customers',
    'hero-stat-2': 'Years Experience',
    'hero-stat-3': 'Emergency Service',
    'hero-call-btn': 'Call Now',
    'hero-services-btn': 'Our Services',
    'hero-certified': 'Certified Professional',
    'hero-card-1-title': 'Premium Quality',
    'hero-card-1-text': 'Top-grade materials',
    'hero-card-2-title': 'Fast Response',
    'hero-card-2-text': 'Same day service',
    'hero-card-3-title': 'Guaranteed Work',
    'hero-card-3-text': 'Full warranty'
  }
};

// Listen for language change events
window.addEventListener('languageChanged', function(e) {
  const lang = e.detail.language;
  Object.keys(heroTranslations[lang]).forEach(key => {
    const element = document.getElementById(key);
    if (element) {
      element.textContent = heroTranslations[lang][key];
    }
  });
});
</script>
<!-- /wp:html -->
