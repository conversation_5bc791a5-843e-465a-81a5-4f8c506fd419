export interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  service: string;
  message: string;
  type: 'contact' | 'quote' | 'emergency';
}

export const sendEmail = async (formData: ContactFormData): Promise<boolean> => {
  try {
    console.log('🔄 Starting email send process...');
    console.log('📧 Form data received:', formData);
    
    // Check if EmailJS is loaded
    if (!window.emailjs) {
      console.error('❌ EmailJS not loaded!');
      return false;
    }
    
    console.log('✅ EmailJS is loaded');
    
    // Template parameters matching your EmailJS template exactly
    const templateParams = {
      from_name: formData.name,
      from_email: formData.email,
      phone: formData.phone,
      service: formData.service,
      type: formData.type,
      message: formData.message,
      to_name: 'Kyria<PERSON> Plumber' // Added this for completeness
    };

    console.log('📋 Template parameters:', templateParams);
    console.log('🔑 Using Service ID: service_gdrd50p');
    console.log('📄 Using Template ID: template_o8stalu');

    // Send email via EmailJS
    const response = await window.emailjs.send(
      'service_gdrd50p',  // Your service ID
      'template_o8stalu', // Your template ID
      templateParams
    );

    console.log('📨 EmailJS Response:', response);
    console.log('📊 Response Status:', response.status);
    console.log('📝 Response Text:', response.text);

    if (response.status === 200) {
      console.log('✅ Email sent successfully!');
      alert('✅ Email sent successfully! Check your inbox.');
      return true;
    } else {
      console.error('❌ EmailJS failed with status:', response.status);
      alert('❌ Email failed to send. Status: ' + response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ EmailJS Error Details:', error);
    console.error('❌ Error message:', error.message);
    console.error('❌ Error stack:', error.stack);
    alert('❌ Email error: ' + error.message);
    return false;
  }
};

// Add global type declaration
declare global {
  interface Window {
    emailjs: any;
  }
}