# WordPress Gutenberg Blocks - Kyriakis Plumber Website

This folder contains all the HTML blocks that you can copy and paste into WordPress Gutenberg Custom HTML blocks to recreate your entire website.

## 📋 Installation Instructions

### Step 1: Copy Each Block
1. Open each `.html` file in this folder
2. Copy the entire content of each file
3. In WordPress, create a new page or edit an existing one
4. Add a **Custom HTML** block for each section
5. Paste the corresponding HTML code into each block

### Step 2: Block Order
Add the blocks in this exact order for the complete website:

1. **01-header-block.html** - Header with navigation and language switcher
2. **02-hero-block.html** - Hero section with main content
3. **03-services-block.html** - Services showcase
4. **04-about-block.html** - About section with statistics
5. **05-contact-block.html** - Contact form (requires EmailJS setup)
6. **06-footer-block.html** - Footer with contact info

### Step 3: Upload Images
Upload these images to your WordPress Media Library:
- `Λεβητοστάσιο.jpg`
- `<PERSON><PERSON>ια<PERSON><PERSON> συστήματα.jpg`
- `Ενδοδαπεδια θερμανση.jpg`
- `Θερμοσίφωνο.jpg`
- `Επαγγελματική επισκευή υδραυλικών προβλημάτων με Εγγύηση ποιότητας.jpg`
- `logo-valsir-en-gb.png`
- `logo-aquatherm.png`
- `uponor.jpg`
- `rahau-sunergasia.jpg.webp`

### Step 4: EmailJS Setup (Required for Contact Form)
The contact form requires EmailJS to work. Follow these steps:

1. **Sign up for EmailJS**: Go to [https://www.emailjs.com/](https://www.emailjs.com/)
2. **Create a service**: Connect your email provider (Gmail, Outlook, etc.)
3. **Create a template**: Set up an email template with these variables:
   - `{{from_name}}` - Sender's name
   - `{{from_email}}` - Sender's email
   - `{{phone}}` - Phone number
   - `{{service}}` - Selected service
   - `{{type}}` - Form type (contact/quote/emergency)
   - `{{message}}` - Message content
   - `{{to_name}}` - Your company name

4. **Update the contact form**: In `05-contact-block.html`, replace:
   - `YOUR_EMAILJS_PUBLIC_KEY` with your actual public key
   - `YOUR_EMAILJS_SERVICE_ID` with your actual service ID
   - `YOUR_EMAILJS_TEMPLATE_ID` with your actual template ID

## ✨ Features Included

### 🌐 Bilingual Support (Greek/English)
- Complete language switcher in header
- All content translates automatically
- Professional translations for both languages

### 📱 Fully Responsive Design
- Mobile-first approach
- Tablet and desktop optimized
- Touch-friendly navigation

### 🎨 Modern 2025 Design
- Glassmorphism effects
- Gradient backgrounds
- Smooth animations and transitions
- Apple-level design aesthetics

### 📞 Contact Integration
- Working contact form with EmailJS
- Phone call buttons
- WhatsApp integration
- Multiple contact methods

### ⚡ Performance Optimized
- Lightweight CSS
- Optimized animations
- Fast loading times
- SEO-friendly structure

## 🔧 Customization

### Colors
Each block uses CSS custom properties. You can easily change colors by modifying the gradient values in the `<style>` sections.

### Content
All text content is easily editable within the HTML. Look for:
- Phone numbers: `+306985814213`
- Email: `<EMAIL>`
- Company name: `Kyriakis Plumber`
- WhatsApp links: `https://wa.me/306985814213`

### Images
Replace image paths with your WordPress media URLs:
```html
<!-- Change this -->
<img src="/wp-content/uploads/image-name.jpg" alt="Description">

<!-- To your actual WordPress media URL -->
<img src="https://yoursite.com/wp-content/uploads/2025/01/image-name.jpg" alt="Description">
```

## 🚀 Going Live

1. **Test all functionality**: Make sure all buttons, forms, and links work
2. **Verify EmailJS**: Send test emails through the contact form
3. **Check mobile responsiveness**: Test on different devices
4. **Optimize images**: Compress images for faster loading
5. **SEO setup**: Add proper meta tags and descriptions

## 📞 Support

If you need help with the setup or customization, the blocks are designed to be self-contained and work independently. Each block includes its own CSS and JavaScript, so they won't conflict with your WordPress theme.

## 🎯 Pro Tips

- **Page Speed**: The blocks are optimized for speed, but consider using a caching plugin
- **SEO**: Add proper heading structure (H1, H2, H3) in your WordPress editor
- **Analytics**: Add Google Analytics or similar tracking code
- **Backup**: Always backup your site before making changes

---

**Ready to launch your professional plumbing website! 🚀**