<!-- WordPress Gutenberg Block: Services Section -->
<!-- Copy this entire block into a Custom HTML block in WordPress -->

<style>
.kyriakis-services {
  padding: 6rem 0;
  background: linear-gradient(135deg, #f9fafb 0%, #dbeafe 100%);
}

.kyriakis-services-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

.kyriakis-services-header {
  text-align: center;
  margin-bottom: 5rem;
}

.kyriakis-services-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.kyriakis-services-badge-icon {
  background: linear-gradient(45deg, #2563eb, #9333ea);
  padding: 0.75rem;
  border-radius: 1rem;
}

.kyriakis-services-badge-text {
  color: #2563eb;
  font-weight: 600;
  font-size: 1.125rem;
}

.kyriakis-services-title {
  font-size: 3rem;
  font-weight: bold;
  background: linear-gradient(45deg, #111827, #374151);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
}

@media (min-width: 1024px) {
  .kyriakis-services-title {
    font-size: 3.75rem;
  }
}

.kyriakis-services-description {
  font-size: 1.25rem;
  color: #4b5563;
  max-width: 48rem;
  margin: 0 auto;
  line-height: 1.6;
}

.kyriakis-services-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

@media (min-width: 768px) {
  .kyriakis-services-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (min-width: 1024px) {
  .kyriakis-services-grid {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

.kyriakis-service-card {
  background: white;
  border-radius: 1.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  transition: all 0.5s ease;
  transform: translateY(0);
  border: 1px solid #f3f4f6;
  position: relative;
}

.kyriakis-service-card:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: translateY(-12px);
}

.kyriakis-service-image {
  position: relative;
  height: 14rem;
  overflow: hidden;
}

.kyriakis-service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.7s ease;
}

.kyriakis-service-card:hover .kyriakis-service-image img {
  transform: scale(1.1);
}

.kyriakis-service-image::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.2) 50%, transparent 100%);
}

.kyriakis-service-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  color: white;
  font-size: 0.875rem;
  font-weight: bold;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.kyriakis-service-icon {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  padding: 1rem;
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.kyriakis-service-content {
  padding: 2rem;
}

.kyriakis-service-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #111827;
  margin-bottom: 1rem;
  transition: color 0.3s ease;
}

.kyriakis-service-card:hover .kyriakis-service-title {
  color: #2563eb;
}

.kyriakis-service-description {
  color: #4b5563;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.kyriakis-service-features {
  margin-bottom: 2rem;
}

.kyriakis-service-feature {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #4b5563;
  margin-bottom: 0.75rem;
}

.kyriakis-service-feature-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  margin-right: 0.75rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.kyriakis-service-btn {
  width: 100%;
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 1rem;
  font-weight: 600;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  border: none;
  cursor: pointer;
}

.kyriakis-service-btn:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: scale(1.05);
  color: white;
  text-decoration: none;
}

.kyriakis-service-overlay {
  position: absolute;
  inset: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 1.5rem;
}

.kyriakis-service-card:hover .kyriakis-service-overlay {
  opacity: 0.05;
}

.kyriakis-partners {
  margin-top: 6rem;
  text-align: center;
}

.kyriakis-partners-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 2rem;
}

.kyriakis-partners-title h3 {
  font-size: 1.875rem;
  font-weight: bold;
  color: #111827;
  margin: 0;
}

.kyriakis-partners-grid {
  background: white;
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid #f3f4f6;
}

.kyriakis-partners-logos {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  align-items: center;
}

@media (min-width: 768px) {
  .kyriakis-partners-logos {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }
}

.kyriakis-partner-logo {
  transition: all 0.3s ease;
  filter: grayscale(100%);
}

.kyriakis-partner-logo:hover {
  filter: grayscale(0%);
  transform: scale(1.1);
}

.kyriakis-partner-logo img {
  height: 4rem;
  width: auto;
  max-width: 100%;
}

/* Icons */
.kyriakis-icon {
  width: 1.5rem;
  height: 1.5rem;
  fill: currentColor;
}

.kyriakis-icon-lg {
  width: 3rem;
  height: 3rem;
  fill: currentColor;
}

.kyriakis-icon-star {
  width: 1.5rem;
  height: 1.5rem;
  color: #fbbf24;
  fill: currentColor;
}
</style>

<section id="services" class="kyriakis-services">
  <div class="kyriakis-services-container">
    <div class="kyriakis-services-header">
      <div class="kyriakis-services-badge">
        <div class="kyriakis-services-badge-icon">
          <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
          </svg>
        </div>
        <span class="kyriakis-services-badge-text" id="services-badge">Υπηρεσίες 2025</span>
      </div>
      <h2 class="kyriakis-services-title" id="services-title">Οι Υπηρεσίες Μας</h2>
      <p class="kyriakis-services-description" id="services-description">
        Προσφέρουμε ολοκληρωμένες θερμοϋδραυλικές υπηρεσίες με σύγχρονα υλικά, τεχνολογία αιχμής και εγγύηση ποιότητας
      </p>
    </div>

    <div class="kyriakis-services-grid">
      <!-- Service 1: Boiler Installation -->
      <div class="kyriakis-service-card">
        <div class="kyriakis-service-image">
          <img src="/wp-content/uploads/Λεβητοστάσιο.jpg" alt="Εγκατάσταση Λεβητοστασίου">
          <div class="kyriakis-service-badge" style="background: linear-gradient(45deg, #ef4444, #f97316);">
            <span id="service-boiler-badge">Δημοφιλές</span>
          </div>
          <div class="kyriakis-service-icon" style="background: linear-gradient(45deg, #ef4444, #f97316);">
            <svg class="kyriakis-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z"/>
            </svg>
          </div>
        </div>
        <div class="kyriakis-service-content">
          <h3 class="kyriakis-service-title" id="service-boiler-title">Εγκατάσταση Λεβητοστασίου</h3>
          <p class="kyriakis-service-description" id="service-boiler-description">
            Πλήρης εγκατάσταση λεβητοστασίου με σύγχρονα συστήματα θέρμανσης και υψηλή ενεργειακή απόδοση.
          </p>
          <div class="kyriakis-service-features">
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #ef4444, #f97316);"></div>
              Σχεδιασμός συστήματος
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #ef4444, #f97316);"></div>
              Εγκατάσταση λέβητα
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #ef4444, #f97316);"></div>
              Δοκιμές ασφαλείας
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #ef4444, #f97316);"></div>
              Εγγύηση 5 ετών
            </div>
          </div>
          <button class="kyriakis-service-btn" style="background: linear-gradient(45deg, #ef4444, #f97316);" onclick="scrollToSection('contact')">
            <span id="service-learn">Μάθετε Περισσότερα</span>
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="5" y1="12" x2="19" y2="12"/>
              <polyline points="12,5 19,12 12,19"/>
            </svg>
          </button>
        </div>
        <div class="kyriakis-service-overlay" style="background: linear-gradient(45deg, #ef4444, #f97316);"></div>
      </div>

      <!-- Service 2: Boiler Service -->
      <div class="kyriakis-service-card">
        <div class="kyriakis-service-image">
          <img src="/wp-content/uploads/Λεβητοστάσιο.jpg" alt="Service Λεβητοστασίου">
          <div class="kyriakis-service-badge" style="background: linear-gradient(45deg, #3b82f6, #06b6d4);">
            <span id="service-service-badge">Συντήρηση</span>
          </div>
          <div class="kyriakis-service-icon" style="background: linear-gradient(45deg, #3b82f6, #06b6d4);">
            <svg class="kyriakis-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="3"/>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
            </svg>
          </div>
        </div>
        <div class="kyriakis-service-content">
          <h3 class="kyriakis-service-title" id="service-service-title">Service Λεβητοστασίου</h3>
          <p class="kyriakis-service-description" id="service-service-description">
            Τακτική συντήρηση και επισκευή λεβητοστασίων για βέλτιστη λειτουργία και ασφάλεια.
          </p>
          <div class="kyriakis-service-features">
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #3b82f6, #06b6d4);"></div>
              Καθαρισμός καυστήρα
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #3b82f6, #06b6d4);"></div>
              Έλεγχος ασφαλείας
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #3b82f6, #06b6d4);"></div>
              Ρύθμιση παραμέτρων
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #3b82f6, #06b6d4);"></div>
              Αντικατάσταση φίλτρων
            </div>
          </div>
          <button class="kyriakis-service-btn" style="background: linear-gradient(45deg, #3b82f6, #06b6d4);" onclick="scrollToSection('contact')">
            <span id="service-learn-2">Μάθετε Περισσότερα</span>
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="5" y1="12" x2="19" y2="12"/>
              <polyline points="12,5 19,12 12,19"/>
            </svg>
          </button>
        </div>
        <div class="kyriakis-service-overlay" style="background: linear-gradient(45deg, #3b82f6, #06b6d4);"></div>
      </div>

      <!-- Service 3: Solar Systems -->
      <div class="kyriakis-service-card">
        <div class="kyriakis-service-image">
          <img src="/wp-content/uploads/Ηλιακό συστήματα.jpg" alt="Ηλιακά Συστήματα">
          <div class="kyriakis-service-badge" style="background: linear-gradient(45deg, #fbbf24, #f97316);">
            <span id="service-solar-badge">Οικολογικό</span>
          </div>
          <div class="kyriakis-service-icon" style="background: linear-gradient(45deg, #fbbf24, #f97316);">
            <svg class="kyriakis-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="5"/>
              <line x1="12" y1="1" x2="12" y2="3"/>
              <line x1="12" y1="21" x2="12" y2="23"/>
              <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
              <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
              <line x1="1" y1="12" x2="3" y2="12"/>
              <line x1="21" y1="12" x2="23" y2="12"/>
              <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
              <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
            </svg>
          </div>
        </div>
        <div class="kyriakis-service-content">
          <h3 class="kyriakis-service-title" id="service-solar-title">Ηλιακά Συστήματα</h3>
          <p class="kyriakis-service-description" id="service-solar-description">
            Εγκατάσταση ηλιακών συλλεκτών για οικονομική και οικολογική θέρμανση νερού.
          </p>
          <div class="kyriakis-service-features">
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #fbbf24, #f97316);"></div>
              Ηλιακοί συλλέκτες
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #fbbf24, #f97316);"></div>
              Μπόιλερ ηλιακού
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #fbbf24, #f97316);"></div>
              Αυτοματισμοί
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #fbbf24, #f97316);"></div>
              Εξοικονόμηση 70%
            </div>
          </div>
          <button class="kyriakis-service-btn" style="background: linear-gradient(45deg, #fbbf24, #f97316);" onclick="scrollToSection('contact')">
            <span id="service-learn-3">Μάθετε Περισσότερα</span>
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="5" y1="12" x2="19" y2="12"/>
              <polyline points="12,5 19,12 12,19"/>
            </svg>
          </button>
        </div>
        <div class="kyriakis-service-overlay" style="background: linear-gradient(45deg, #fbbf24, #f97316);"></div>
      </div>

      <!-- Service 4: Underfloor Heating -->
      <div class="kyriakis-service-card">
        <div class="kyriakis-service-image">
          <img src="/wp-content/uploads/Ενδοδαπεδια θερμανση.jpg" alt="Ενδοδαπέδια Θέρμανση">
          <div class="kyriakis-service-badge" style="background: linear-gradient(45deg, #9333ea, #ec4899);">
            <span id="service-underfloor-badge">Σύγχρονο</span>
          </div>
          <div class="kyriakis-service-icon" style="background: linear-gradient(45deg, #9333ea, #ec4899);">
            <svg class="kyriakis-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z"/>
            </svg>
          </div>
        </div>
        <div class="kyriakis-service-content">
          <h3 class="kyriakis-service-title" id="service-underfloor-title">Ενδοδαπέδια Θέρμανση</h3>
          <p class="kyriakis-service-description" id="service-underfloor-description">
            Σύγχρονα συστήματα ενδοδαπέδιας θέρμανσης για ομοιόμορφη κατανομή θερμότητας.
          </p>
          <div class="kyriakis-service-features">
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #9333ea, #ec4899);"></div>
              Σωληνώσεις PE-X
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #9333ea, #ec4899);"></div>
              Θερμοστάτες δωματίου
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #9333ea, #ec4899);"></div>
              Συλλέκτες διανομής
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #9333ea, #ec4899);"></div>
              Οικονομία 30%
            </div>
          </div>
          <button class="kyriakis-service-btn" style="background: linear-gradient(45deg, #9333ea, #ec4899);" onclick="scrollToSection('contact')">
            <span id="service-learn-4">Μάθετε Περισσότερα</span>
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="5" y1="12" x2="19" y2="12"/>
              <polyline points="12,5 19,12 12,19"/>
            </svg>
          </button>
        </div>
        <div class="kyriakis-service-overlay" style="background: linear-gradient(45deg, #9333ea, #ec4899);"></div>
      </div>

      <!-- Service 5: Water Heater -->
      <div class="kyriakis-service-card">
        <div class="kyriakis-service-image">
          <img src="/wp-content/uploads/Θερμοσίφωνο.jpg" alt="Θερμοσίφωνο">
          <div class="kyriakis-service-badge" style="background: linear-gradient(45deg, #10b981, #059669);">
            <span id="service-heater-badge">Αξιόπιστο</span>
          </div>
          <div class="kyriakis-service-icon" style="background: linear-gradient(45deg, #10b981, #059669);">
            <svg class="kyriakis-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z"/>
              <path d="M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2.04 4.9 4.14 6.4s3.86 3.5 3.86 5.8a7.3 7.3 0 0 1-.84 3.31c-.53.84-1.11 1.54-1.71 2.1"/>
            </svg>
          </div>
        </div>
        <div class="kyriakis-service-content">
          <h3 class="kyriakis-service-title" id="service-heater-title">Θερμοσίφωνο</h3>
          <p class="kyriakis-service-description" id="service-heater-description">
            Εγκατάσταση και επισκευή θερμοσιφώνων για αξιόπιστη παροχή ζεστού νερού.
          </p>
          <div class="kyriakis-service-features">
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #10b981, #059669);"></div>
              Ηλεκτρικοί θερμοσίφωνες
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #10b981, #059669);"></div>
              Θερμοσίφωνες αερίου
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #10b981, #059669);"></div>
              Στιγμιαίοι θερμαντήρες
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #10b981, #059669);"></div>
              Εξοικονόμηση ενέργειας
            </div>
          </div>
          <button class="kyriakis-service-btn" style="background: linear-gradient(45deg, #10b981, #059669);" onclick="scrollToSection('contact')">
            <span id="service-learn-5">Μάθετε Περισσότερα</span>
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="5" y1="12" x2="19" y2="12"/>
              <polyline points="12,5 19,12 12,19"/>
            </svg>
          </button>
        </div>
        <div class="kyriakis-service-overlay" style="background: linear-gradient(45deg, #10b981, #059669);"></div>
      </div>

      <!-- Service 6: Plumbing Repair -->
      <div class="kyriakis-service-card">
        <div class="kyriakis-service-image">
          <img src="/wp-content/uploads/Επαγγελματική επισκευή υδραυλικών προβλημάτων με Εγγύηση ποιότητας.jpg" alt="Επισκευή Υδραυλικών">
          <div class="kyriakis-service-badge" style="background: linear-gradient(45deg, #6366f1, #9333ea);">
            <span id="service-repair-badge">24/7</span>
          </div>
          <div class="kyriakis-service-icon" style="background: linear-gradient(45deg, #6366f1, #9333ea);">
            <svg class="kyriakis-icon-lg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/>
            </svg>
          </div>
        </div>
        <div class="kyriakis-service-content">
          <h3 class="kyriakis-service-title" id="service-repair-title">Επισκευή Υδραυλικών</h3>
          <p class="kyriakis-service-description" id="service-repair-description">
            Επαγγελματική επισκευή όλων των υδραυλικών προβλημάτων με εγγύηση ποιότητας.
          </p>
          <div class="kyriakis-service-features">
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #6366f1, #9333ea);"></div>
              Διαρροές σωλήνων
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #6366f1, #9333ea);"></div>
              Βουλώματα αποχέτευσης
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #6366f1, #9333ea);"></div>
              Επισκευή βρυσών
            </div>
            <div class="kyriakis-service-feature">
              <div class="kyriakis-service-feature-dot" style="background: linear-gradient(45deg, #6366f1, #9333ea);"></div>
              24/7 Επείγοντα
            </div>
          </div>
          <button class="kyriakis-service-btn" style="background: linear-gradient(45deg, #6366f1, #9333ea);" onclick="scrollToSection('contact')">
            <span id="service-learn-6">Μάθετε Περισσότερα</span>
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="5" y1="12" x2="19" y2="12"/>
              <polyline points="12,5 19,12 12,19"/>
            </svg>
          </button>
        </div>
        <div class="kyriakis-service-overlay" style="background: linear-gradient(45deg, #6366f1, #9333ea);"></div>
      </div>
    </div>

    <!-- Partner Logos Section -->
    <div class="kyriakis-partners">
      <div class="kyriakis-partners-title">
        <svg class="kyriakis-icon-star" viewBox="0 0 24 24" fill="currentColor">
          <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
        </svg>
        <h3 id="services-partners">Συνεργαζόμαστε με τις Καλύτερες Εταιρείες</h3>
        <svg class="kyriakis-icon-star" viewBox="0 0 24 24" fill="currentColor">
          <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
        </svg>
      </div>
      <div class="kyriakis-partners-grid">
        <div class="kyriakis-partners-logos">
          <div class="kyriakis-partner-logo">
            <img src="/wp-content/uploads/logo-valsir-en-gb.png" alt="Valsir">
          </div>
          <div class="kyriakis-partner-logo">
            <img src="/wp-content/uploads/logo-aquatherm.png" alt="Aquatherm">
          </div>
          <div class="kyriakis-partner-logo">
            <img src="/wp-content/uploads/uponor.jpg" alt="Uponor">
          </div>
          <div class="kyriakis-partner-logo">
            <img src="/wp-content/uploads/rahau-sunergasia.jpg.webp" alt="Rehau">
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
// Services translations
const servicesTranslations = {
  gr: {
    'services-badge': 'Υπηρεσίες 2025',
    'services-title': 'Οι Υπηρεσίες Μας',
    'services-description': 'Προσφέρουμε ολοκληρωμένες θερμοϋδραυλικές υπηρεσίες με σύγχρονα υλικά, τεχνολογία αιχμής και εγγύηση ποιότητας',
    'service-boiler-badge': 'Δημοφιλές',
    'service-boiler-title': 'Εγκατάσταση Λεβητοστασίου',
    'service-boiler-description': 'Πλήρης εγκατάσταση λεβητοστασίου με σύγχρονα συστήματα θέρμανσης και υψηλή ενεργειακή απόδοση.',
    'service-service-badge': 'Συντήρηση',
    'service-service-title': 'Service Λεβητοστασίου',
    'service-service-description': 'Τακτική συντήρηση και επισκευή λεβητοστασίων για βέλτιστη λειτουργία και ασφάλεια.',
    'service-solar-badge': 'Οικολογικό',
    'service-solar-title': 'Ηλιακά Συστήματα',
    'service-solar-description': 'Εγκατάσταση ηλιακών συλλεκτών για οικονομική και οικολογική θέρμανση νερού.',
    'service-underfloor-badge': 'Σύγχρονο',
    'service-underfloor-title': 'Ενδοδαπέδια Θέρμανση',
    'service-underfloor-description': 'Σύγχρονα συστήματα ενδοδαπέδιας θέρμανσης για ομοιόμορφη κατανομή θερμότητας.',
    'service-heater-badge': 'Αξιόπιστο',
    'service-heater-title': 'Θερμοσίφωνο',
    'service-heater-description': 'Εγκατάσταση και επισκευή θερμοσιφώνων για αξιόπιστη παροχή ζεστού νερού.',
    'service-repair-badge': '24/7',
    'service-repair-title': 'Επισκευή Υδραυλικών',
    'service-repair-description': 'Επαγγελματική επισκευή όλων των υδραυλικών προβλημάτων με εγγύηση ποιότητας.',
    'service-learn': 'Μάθετε Περισσότερα',
    'service-learn-2': 'Μάθετε Περισσότερα',
    'service-learn-3': 'Μάθετε Περισσότερα',
    'service-learn-4': 'Μάθετε Περισσότερα',
    'service-learn-5': 'Μάθετε Περισσότερα',
    'service-learn-6': 'Μάθετε Περισσότερα',
    'services-partners': 'Συνεργαζόμαστε με τις Καλύτερες Εταιρείες'
  },
  en: {
    'services-badge': '2025 Services',
    'services-title': 'Our Services',
    'services-description': 'We offer comprehensive plumbing and heating services with modern materials, cutting-edge technology and quality guarantee',
    'service-boiler-badge': 'Popular',
    'service-boiler-title': 'Boiler Installation',
    'service-boiler-description': 'Complete boiler room installation with modern heating systems and high energy efficiency.',
    'service-service-badge': 'Maintenance',
    'service-service-title': 'Boiler Service',
    'service-service-description': 'Regular maintenance and repair of boiler rooms for optimal operation and safety.',
    'service-solar-badge': 'Eco-Friendly',
    'service-solar-title': 'Solar Systems',
    'service-solar-description': 'Installation of solar collectors for economical and ecological water heating.',
    'service-underfloor-badge': 'Modern',
    'service-underfloor-title': 'Underfloor Heating',
    'service-underfloor-description': 'Modern underfloor heating systems for uniform heat distribution.',
    'service-heater-badge': 'Reliable',
    'service-heater-title': 'Water Heater',
    'service-heater-description': 'Installation and repair of water heaters for reliable hot water supply.',
    'service-repair-badge': '24/7',
    'service-repair-title': 'Plumbing Repair',
    'service-repair-description': 'Professional repair of all plumbing problems with quality guarantee.',
    'service-learn': 'Learn More',
    'service-learn-2': 'Learn More',
    'service-learn-3': 'Learn More',
    'service-learn-4': 'Learn More',
    'service-learn-5': 'Learn More',
    'service-learn-6': 'Learn More',
    'services-partners': 'We Partner with the Best Companies'
  }
};

// Listen for language changes
window.addEventListener('languageChanged', function(e) {
  const lang = e.detail.language;
  const translations = servicesTranslations[lang];
  
  Object.keys(translations).forEach(key => {
    const element = document.getElementById(key);
    if (element) {
      element.textContent = translations[key];
    }
  });
});

// Smooth scroll function
function scrollToSection(sectionId) {
  const element = document.getElementById(sectionId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
}
</script>