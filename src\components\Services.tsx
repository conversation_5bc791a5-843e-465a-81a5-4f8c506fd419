import React, { useState } from 'react';
import { Flame, Sun, Thermometer, Droplets, Wrench, <PERSON>ting<PERSON>, ArrowRight, Star, Zap } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const Services: React.FC = () => {
  const [hoveredService, setHoveredService] = useState<number | null>(null);
  const { t } = useLanguage();

  const services = [
    {
      icon: <Flame className="w-12 h-12" />,
      title: t('services.boiler.title'),
      description: t('services.boiler.description'),
      image: '/Λεβητοστάσιο.jpg',
      features: ['Σχεδιασμός συστήματος', 'Εγκατάσταση λέβητα', 'Δοκιμές ασφαλείας', 'Εγγύηση 5 ετών'],
      color: 'from-red-500 to-orange-500',
      badge: t('services.boiler.badge')
    },
    {
      icon: <Settings className="w-12 h-12" />,
      title: t('services.service.title'),
      description: t('services.service.description'),
      image: '/Λεβητοστάσιο.jpg',
      features: ['Καθαρισμός καυστήρα', 'Έλεγχος ασφαλείας', 'Ρύθμιση παραμέτρων', 'Αντικατάσταση φίλτρων'],
      color: 'from-blue-500 to-cyan-500',
      badge: t('services.service.badge')
    },
    {
      icon: <Sun className="w-12 h-12" />,
      title: t('services.solar.title'),
      description: t('services.solar.description'),
      image: '/Ηλιακό συστήματα.jpg',
      features: ['Ηλιακοί συλλέκτες', 'Μπόιλερ ηλιακού', 'Αυτοματισμοί', 'Εξοικονόμηση 70%'],
      color: 'from-yellow-500 to-orange-500',
      badge: t('services.solar.badge')
    },
    {
      icon: <Thermometer className="w-12 h-12" />,
      title: t('services.underfloor.title'),
      description: t('services.underfloor.description'),
      image: '/Ενδοδαπεδια θερμανση.jpg',
      features: ['Σωληνώσεις PE-X', 'Θερμοστάτες δωματίου', 'Συλλέκτες διανομής', 'Οικονομία 30%'],
      color: 'from-purple-500 to-pink-500',
      badge: t('services.underfloor.badge')
    },
    {
      icon: <Droplets className="w-12 h-12" />,
      title: t('services.heater.title'),
      description: t('services.heater.description'),
      image: '/Θερμοσίφωνο.jpg',
      features: ['Ηλεκτρικοί θερμοσίφωνες', 'Θερμοσίφωνες αερίου', 'Στιγμιαίοι θερμαντήρες', 'Εξοικονόμηση ενέργειας'],
      color: 'from-green-500 to-emerald-500',
      badge: t('services.heater.badge')
    },
    {
      icon: <Wrench className="w-12 h-12" />,
      title: t('services.repair.title'),
      description: t('services.repair.description'),
      image: '/Επαγγελματική επισκευή υδραυλικών προβλημάτων με Εγγύηση ποιότητας.jpg',
      features: ['Διαρροές σωλήνων', 'Βουλώματα αποχέτευσης', 'Επισκευή βρυσών', '24/7 Επείγοντα'],
      color: 'from-indigo-500 to-purple-500',
      badge: t('services.repair.badge')
    }
  ];

  return (
    <section id="services" className="py-24 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-20">
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-2xl">
              <Zap className="w-6 h-6 text-white" />
            </div>
            <span className="text-blue-600 font-semibold text-lg">{t('services.badge')}</span>
          </div>
          <h2 className="text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-6">
            {t('services.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t('services.description')}
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div
              key={index}
              className="group relative"
              onMouseEnter={() => setHoveredService(index)}
              onMouseLeave={() => setHoveredService(null)}
            >
              <div className="bg-white rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 border border-gray-100">
                {/* Image Section */}
                <div className="relative h-56 overflow-hidden">
                  <img
                    src={service.image}
                    alt={service.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                  
                  {/* Badge */}
                  <div className="absolute top-4 right-4">
                    <div className={`bg-gradient-to-r ${service.color} px-4 py-2 rounded-full text-white text-sm font-bold shadow-lg`}>
                      {service.badge}
                    </div>
                  </div>
                  
                  {/* Icon */}
                  <div className="absolute bottom-4 left-4">
                    <div className={`bg-gradient-to-r ${service.color} p-4 rounded-2xl shadow-xl`}>
                      {service.icon}
                    </div>
                  </div>
                </div>
                
                {/* Content */}
                <div className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {service.description}
                  </p>
                  
                  {/* Features */}
                  <ul className="space-y-3 mb-8">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                        <div className={`w-2 h-2 bg-gradient-to-r ${service.color} rounded-full mr-3 shadow-sm`}></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  
                  {/* CTA Button */}
                  <button className={`w-full bg-gradient-to-r ${service.color} text-white py-4 px-6 rounded-2xl font-semibold hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-3 group-hover:scale-105`}>
                    {t('services.learn')}
                    <ArrowRight className={`w-5 h-5 transition-transform duration-300 ${hoveredService === index ? 'translate-x-1' : ''}`} />
                  </button>
                </div>

                {/* Hover Effect Overlay */}
                <div className={`absolute inset-0 bg-gradient-to-r ${service.color} opacity-0 group-hover:opacity-5 transition-opacity duration-300 rounded-3xl`}></div>
              </div>
            </div>
          ))}
        </div>

        {/* Partner Logos Section */}
        <div className="mt-24 text-center">
          <div className="flex items-center justify-center gap-3 mb-8">
            <Star className="w-6 h-6 text-yellow-500" />
            <h3 className="text-3xl font-bold text-gray-900">{t('services.partners')}</h3>
            <Star className="w-6 h-6 text-yellow-500" />
          </div>
          <div className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center">
              {[
                { src: '/logo-valsir-en-gb.png', alt: 'Valsir' },
                { src: '/logo-aquatherm.png', alt: 'Aquatherm' },
                { src: '/uponor.jpg', alt: 'Uponor' },
                { src: '/rahau-sunergasia.jpg.webp', alt: 'Rehau' }
              ].map((logo, index) => (
                <div key={index} className="group">
                  <img 
                    src={logo.src} 
                    alt={logo.alt} 
                    className="h-16 mx-auto filter grayscale hover:grayscale-0 transition-all duration-300 group-hover:scale-110" 
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;