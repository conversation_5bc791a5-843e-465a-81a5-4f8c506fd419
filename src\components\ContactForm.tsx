import React, { useState } from 'react';
import { Phone, Mail, MapPin, Clock, Wrench, AlertTriangle, Send, Zap, Star, MessageCircle } from 'lucide-react';
import { sendEmail, ContactFormData } from './EmailService';
import { useLanguage } from '../contexts/LanguageContext';

const ContactForm: React.FC = () => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    phone: '',
    service: '',
    message: '',
    type: 'contact'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const services = [
    t('services.boiler.title'),
    t('services.service.title'),
    t('services.solar.title'),
    t('services.underfloor.title'),
    t('services.heater.title'),
    t('services.repair.title'),
    'Εγκατάσταση σωληνώσεων',
    'Άλλο'
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent, type: 'contact' | 'quote' | 'emergency' = 'contact') => {
    e.preventDefault();
    
    if (!formData.name || !formData.email || !formData.phone) {
      alert('Παρακαλώ συμπληρώστε όλα τα απαιτούμενα πεδία');
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      const emailData: ContactFormData = {
        ...formData,
        type
      };
      
      const success = await sendEmail(emailData);
      
      if (success) {
        setSubmitStatus('success');
        setFormData({
          name: '',
          email: '',
          phone: '',
          service: '',
          message: '',
          type: 'contact'
        });
      } else {
        setSubmitStatus('error');
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-3xl shadow-2xl p-8 lg:p-12 max-w-6xl mx-auto border border-gray-100">
      <div className="text-center mb-12">
        <div className="flex items-center justify-center gap-3 mb-6">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-2xl">
            <Mail className="w-6 h-6 text-white" />
          </div>
          <span className="text-blue-600 font-semibold text-lg">{t('contact.badge')}</span>
        </div>
        <h2 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-4">
          {t('contact.title')}
        </h2>
        <p className="text-xl text-gray-600 leading-relaxed">
          {t('contact.description')}
        </p>
      </div>

      {submitStatus === 'success' && (
        <div className="mb-8 p-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-2xl">
          <div className="flex items-center gap-3">
            <div className="bg-green-500 p-2 rounded-full">
              <Star className="w-5 h-5 text-white" />
            </div>
            <p className="text-green-800 font-semibold text-lg">{t('contact.success')}</p>
          </div>
        </div>
      )}

      {submitStatus === 'error' && (
        <div className="mb-8 p-6 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-2xl">
          <div className="flex items-center gap-3">
            <div className="bg-red-500 p-2 rounded-full">
              <AlertTriangle className="w-5 h-5 text-white" />
            </div>
            <p className="text-red-800 font-semibold text-lg">{t('contact.error')}</p>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="grid md:grid-cols-2 gap-8">
          <div className="space-y-2">
            <label htmlFor="name" className="block text-sm font-semibold text-gray-700 mb-3">
              {t('contact.name')} *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
              className="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-lg hover:border-blue-300"
              placeholder={t('contact.name')}
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-3">
              {t('contact.email')} *
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              required
              className="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-lg hover:border-blue-300"
              placeholder="<EMAIL>"
            />
          </div>
        </div>

        <div className="space-y-2">
          <label htmlFor="phone" className="block text-sm font-semibold text-gray-700 mb-3">
            {t('contact.phone')} *
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
            required
            className="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-lg hover:border-blue-300"
            placeholder="************"
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="service" className="block text-sm font-semibold text-gray-700 mb-3">
            {t('contact.service')}
          </label>
          <select
            id="service"
            name="service"
            value={formData.service}
            onChange={handleInputChange}
            className="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-lg hover:border-blue-300"
          >
            <option value="">{t('contact.select')}</option>
            {services.map((service) => (
              <option key={service} value={service}>
                {service}
              </option>
            ))}
          </select>
        </div>

        <div className="space-y-2">
          <label htmlFor="message" className="block text-sm font-semibold text-gray-700 mb-3">
            {t('contact.message')}
          </label>
          <textarea
            id="message"
            name="message"
            value={formData.message}
            onChange={handleInputChange}
            rows={5}
            className="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 resize-none text-lg hover:border-blue-300"
            placeholder={t('contact.placeholder')}
          />
        </div>

        <div className="grid sm:grid-cols-3 gap-4">
          <button
            type="submit"
            disabled={isSubmitting}
            onClick={(e) => handleSubmit(e, 'contact')}
            className="bg-gradient-to-r from-blue-600 to-cyan-600 text-white py-4 px-8 rounded-2xl font-bold hover:from-blue-700 hover:to-cyan-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-3 hover:scale-105 shadow-xl"
          >
            <Send className="w-5 h-5" />
            {isSubmitting ? t('contact.sending') : t('contact.send')}
          </button>

          <button
            type="button"
            disabled={isSubmitting}
            onClick={(e) => handleSubmit(e, 'quote')}
            className="bg-gradient-to-r from-green-600 to-emerald-600 text-white py-4 px-8 rounded-2xl font-bold hover:from-green-700 hover:to-emerald-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-3 hover:scale-105 shadow-xl"
          >
            <Wrench className="w-5 h-5" />
            {isSubmitting ? t('contact.sending') : t('contact.quote')}
          </button>

          <button
            type="button"
            disabled={isSubmitting}
            onClick={(e) => handleSubmit(e, 'emergency')}
            className="bg-gradient-to-r from-red-600 to-pink-600 text-white py-4 px-8 rounded-2xl font-bold hover:from-red-700 hover:to-pink-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-3 hover:scale-105 shadow-xl"
          >
            <AlertTriangle className="w-5 h-5" />
            {isSubmitting ? t('contact.sending') : t('contact.emergency')}
          </button>
        </div>
      </form>

      <div className="mt-12 pt-8 border-t border-gray-200">
        <div className="grid md:grid-cols-3 gap-8 text-center">
          <div className="group">
            <div className="bg-gradient-to-r from-blue-500 to-cyan-500 p-4 rounded-2xl w-fit mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <Phone className="w-8 h-8 text-white" />
            </div>
            <h3 className="font-bold text-gray-900 text-lg mb-2">{t('contact.phone.title')}</h3>
            <p className="text-gray-600 font-medium">+30 ************</p>
            <p className="text-sm text-blue-600">{t('contact.phone.available')}</p>
            <div className="flex justify-center gap-3 mt-4">
              <a
                href="https://wa.me/306985814213"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-green-500 hover:bg-green-600 text-white p-2 rounded-xl transition-all duration-300 hover:scale-110"
                title="WhatsApp"
              >
                <MessageCircle className="w-5 h-5" />
              </a>
            </div>
          </div>
          
          <div className="group">
            <div className="bg-gradient-to-r from-green-500 to-emerald-500 p-4 rounded-2xl w-fit mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <Mail className="w-8 h-8 text-white" />
            </div>
            <h3 className="font-bold text-gray-900 text-lg mb-2">{t('contact.email.title')}</h3>
            <p className="text-gray-600 font-medium"><EMAIL></p>
            <p className="text-sm text-green-600">{t('contact.email.fast')}</p>
          </div>
          
          <div className="group">
            <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-4 rounded-2xl w-fit mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <Clock className="w-8 h-8 text-white" />
            </div>
            <h3 className="font-bold text-gray-900 text-lg mb-2">{t('contact.hours.title')}</h3>
            <p className="text-gray-600 font-medium">{t('contact.hours.schedule')}</p>
            <p className="text-sm text-purple-600">{t('contact.hours.emergency')}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactForm;