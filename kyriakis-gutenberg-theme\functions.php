<?php
/**
 * Kyriakis Plumber Theme Functions
 * 
 * @package KyriakisPlumber
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme setup
 */
function kyriakis_theme_setup() {
    // Add theme support for various features
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('custom-logo');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script'
    ));
    
    // Add support for block styles
    add_theme_support('wp-block-styles');
    
    // Add support for full and wide align images
    add_theme_support('align-wide');
    
    // Add support for editor styles
    add_theme_support('editor-styles');
    
    // Add support for responsive embeds
    add_theme_support('responsive-embeds');
    
    // Add support for custom line height
    add_theme_support('custom-line-height');
    
    // Add support for custom units
    add_theme_support('custom-units');
    
    // Add support for custom spacing
    add_theme_support('custom-spacing');
    
    // Add support for appearance tools
    add_theme_support('appearance-tools');
    
    // Add support for border
    add_theme_support('border');
    
    // Add support for link color
    add_theme_support('link-color');
    
    // Load text domain for translations
    load_theme_textdomain('kyriakis-plumber', get_template_directory() . '/languages');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'kyriakis-plumber'),
        'footer' => __('Footer Menu', 'kyriakis-plumber'),
    ));
}
add_action('after_setup_theme', 'kyriakis_theme_setup');

/**
 * Enqueue scripts and styles
 */
function kyriakis_theme_scripts() {
    // Enqueue theme stylesheet
    wp_enqueue_style(
        'kyriakis-style',
        get_stylesheet_uri(),
        array(),
        wp_get_theme()->get('Version')
    );
    
    // Enqueue block editor styles
    wp_enqueue_style(
        'kyriakis-editor-style',
        get_template_directory_uri() . '/assets/css/editor-style.css',
        array(),
        wp_get_theme()->get('Version')
    );
    
    // Enqueue custom JavaScript
    wp_enqueue_script(
        'kyriakis-script',
        get_template_directory_uri() . '/assets/js/theme.js',
        array(),
        wp_get_theme()->get('Version'),
        true
    );
    
    // Localize script for AJAX
    wp_localize_script('kyriakis-script', 'kyriakis_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('kyriakis_nonce'),
        'current_language' => get_locale(),
    ));
}
add_action('wp_enqueue_scripts', 'kyriakis_theme_scripts');

/**
 * Enqueue block editor assets
 */
function kyriakis_block_editor_assets() {
    wp_enqueue_script(
        'kyriakis-block-editor',
        get_template_directory_uri() . '/assets/js/block-editor.js',
        array('wp-blocks', 'wp-dom-ready', 'wp-edit-post'),
        wp_get_theme()->get('Version'),
        true
    );
}
add_action('enqueue_block_editor_assets', 'kyriakis_block_editor_assets');

/**
 * Register block patterns
 */
function kyriakis_register_block_patterns() {
    // Register pattern categories
    register_block_pattern_category('kyriakis-patterns', array(
        'label' => __('Kyriakis Plumber', 'kyriakis-plumber'),
    ));
    
    // Register patterns
    $patterns = array(
        'header' => __('Header with Navigation', 'kyriakis-plumber'),
        'hero' => __('Hero Section', 'kyriakis-plumber'),
        'services' => __('Services Showcase', 'kyriakis-plumber'),
        'about' => __('About Section', 'kyriakis-plumber'),
        'contact' => __('Contact Form', 'kyriakis-plumber'),
        'footer' => __('Footer', 'kyriakis-plumber'),
    );
    
    foreach ($patterns as $pattern => $title) {
        $pattern_file = get_template_directory() . '/patterns/' . $pattern . '.php';
        if (file_exists($pattern_file)) {
            register_block_pattern(
                'kyriakis/' . $pattern,
                array(
                    'title' => $title,
                    'categories' => array('kyriakis-patterns'),
                    'content' => file_get_contents($pattern_file),
                )
            );
        }
    }
}
add_action('init', 'kyriakis_register_block_patterns');

/**
 * Add custom image sizes
 */
function kyriakis_custom_image_sizes() {
    add_image_size('kyriakis-service', 400, 300, true);
    add_image_size('kyriakis-hero', 1200, 600, true);
    add_image_size('kyriakis-thumbnail', 300, 200, true);
}
add_action('after_setup_theme', 'kyriakis_custom_image_sizes');

/**
 * Customize excerpt length
 */
function kyriakis_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'kyriakis_excerpt_length');

/**
 * Add custom body classes
 */
function kyriakis_body_classes($classes) {
    $classes[] = 'kyriakis-theme';
    
    if (is_front_page()) {
        $classes[] = 'kyriakis-home';
    }
    
    return $classes;
}
add_filter('body_class', 'kyriakis_body_classes');

/**
 * Remove unwanted WordPress features
 */
function kyriakis_remove_wp_features() {
    // Remove WordPress version from head
    remove_action('wp_head', 'wp_generator');
    
    // Remove RSD link
    remove_action('wp_head', 'rsd_link');
    
    // Remove Windows Live Writer link
    remove_action('wp_head', 'wlwmanifest_link');
    
    // Remove shortlink
    remove_action('wp_head', 'wp_shortlink_wp_head');
}
add_action('init', 'kyriakis_remove_wp_features');

/**
 * Security enhancements
 */
function kyriakis_security_enhancements() {
    // Remove WordPress version from RSS feeds
    add_filter('the_generator', '__return_empty_string');
    
    // Disable XML-RPC
    add_filter('xmlrpc_enabled', '__return_false');
    
    // Remove REST API links from head
    remove_action('wp_head', 'rest_output_link_wp_head');
    remove_action('wp_head', 'wp_oembed_add_discovery_links');
}
add_action('init', 'kyriakis_security_enhancements');
