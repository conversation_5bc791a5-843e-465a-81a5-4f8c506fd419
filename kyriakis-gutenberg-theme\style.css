/*
Theme Name: <PERSON><PERSON><PERSON><PERSON>lumber - <PERSON><PERSON>nberg Theme
Description: A modern, fully Gutenberg-compatible theme for Kyriakis Plumber services. Features bilingual support (Greek/English), modern design with glassmorphism effects, and complete block editor integration.
Version: 1.0.0
Author: Kyriakis Plumber
Text Domain: kyriakis-plumber
Requires at least: 6.0
Tested up to: 6.4
Requires PHP: 7.4
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Tags: block-theme, full-site-editing, custom-background, custom-colors, custom-header, custom-logo, custom-menu, editor-style, featured-images, flexible-header, footer-widgets, full-width-template, one-column, two-columns, translation-ready, blog, business, portfolio
*/

/* 
This is a block theme that uses theme.json for styling.
Most styles are defined in theme.json and block patterns.
This file contains only essential theme styles.
*/

/* Reset and Base Styles */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>bu<PERSON><PERSON>, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* WordPress Core Styles */
.wp-block-group {
    margin: 0;
}

.wp-block-columns {
    margin: 0;
}

.wp-block-column {
    margin: 0;
}

/* Custom Properties for Theme Colors */
:root {
    --kyriakis-primary: #003b6f;
    --kyriakis-secondary: #2563eb;
    --kyriakis-accent: #9333ea;
    --kyriakis-success: #10b981;
    --kyriakis-warning: #f59e0b;
    --kyriakis-error: #ef4444;
    --kyriakis-white: #ffffff;
    --kyriakis-gray-50: #f9fafb;
    --kyriakis-gray-100: #f3f4f6;
    --kyriakis-gray-200: #e5e7eb;
    --kyriakis-gray-300: #d1d5db;
    --kyriakis-gray-400: #9ca3af;
    --kyriakis-gray-500: #6b7280;
    --kyriakis-gray-600: #4b5563;
    --kyriakis-gray-700: #374151;
    --kyriakis-gray-800: #1f2937;
    --kyriakis-gray-900: #111827;
}

/* Utility Classes */
.kyriakis-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1rem;
}

.kyriakis-gradient-bg {
    background: linear-gradient(135deg, var(--kyriakis-primary), var(--kyriakis-secondary));
}

.kyriakis-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.kyriakis-shadow {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.kyriakis-rounded {
    border-radius: 1rem;
}

.kyriakis-transition {
    transition: all 0.3s ease;
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .kyriakis-container {
        padding: 0 0.75rem;
    }
}

/* Block Editor Styles */
.editor-styles-wrapper {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Ensure blocks don't have unwanted margins */
.wp-block {
    max-width: none;
}

/* Custom block styles for better editing experience */
.wp-block[data-type="core/html"] {
    border: 2px dashed #ddd;
    padding: 1rem;
    margin: 1rem 0;
}

.wp-block[data-type="core/html"]:hover {
    border-color: var(--kyriakis-secondary);
}

/* Print styles */
@media print {
    .kyriakis-header,
    .kyriakis-footer,
    .kyriakis-cta-buttons,
    .kyriakis-whatsapp-btn {
        display: none !important;
    }
}
