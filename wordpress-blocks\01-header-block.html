<!-- WordPress Gutenberg Block: Header -->
<!-- Copy this entire block into a Custom HTML block in WordPress -->

<style>
.kyriakis-header {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  transition: all 0.5s ease;
  background: transparent;
}

.kyriakis-header.scrolled {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border-bottom: 1px solid rgba(229, 231, 235, 1);
}

.kyriakis-header-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

.kyriakis-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

.kyriakis-logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  text-decoration: none;
}

.kyriakis-logo-icon {
  position: relative;
}

.kyriakis-logo-main {
  background: linear-gradient(135deg, #2563eb, #9333ea);
  padding: 0.75rem;
  border-radius: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.kyriakis-logo:hover .kyriakis-logo-main {
  transform: scale(1.1);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.kyriakis-logo-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: linear-gradient(45deg, #fbbf24, #f97316);
  border-radius: 50%;
  padding: 0.25rem;
}

.kyriakis-logo-text h1 {
  font-size: 1.5rem;
  font-weight: bold;
  background: linear-gradient(45deg, #111827, #374151);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
  transition: all 0.3s ease;
}

.kyriakis-header.scrolled .kyriakis-logo-text h1 {
  color: #111827;
}

.kyriakis-logo-text p {
  font-size: 0.875rem;
  font-weight: 600;
  color: #60a5fa;
  margin: 0;
  transition: all 0.3s ease;
}

.kyriakis-header.scrolled .kyriakis-logo-text p {
  color: #4b5563;
}

.kyriakis-nav {
  display: none;
  align-items: center;
  gap: 0.25rem;
}

@media (min-width: 1024px) {
  .kyriakis-nav {
    display: flex;
  }
}

.kyriakis-nav-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 0.3s ease;
  background: none;
  border: none;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.9);
}

.kyriakis-nav-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: scale(1.05);
}

.kyriakis-header.scrolled .kyriakis-nav-btn {
  color: #374151;
}

.kyriakis-header.scrolled .kyriakis-nav-btn:hover {
  background: #f3f4f6;
  color: #2563eb;
}

.kyriakis-lang-switcher {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.kyriakis-lang-icon {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 1rem;
  padding: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.kyriakis-lang-buttons {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.kyriakis-lang-btn {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s ease;
  background: none;
  border: none;
  cursor: pointer;
  color: white;
}

.kyriakis-lang-btn.active {
  background: white;
  color: #2563eb;
}

.kyriakis-lang-btn:hover:not(.active) {
  background: rgba(255, 255, 255, 0.2);
}

.kyriakis-cta-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 0.5rem;
}

.kyriakis-call-btn {
  background: linear-gradient(45deg, #2563eb, #9333ea);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 1rem;
  font-weight: 600;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.kyriakis-call-btn:hover {
  background: linear-gradient(45deg, #1d4ed8, #7c3aed);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
  color: white;
  text-decoration: none;
}

.kyriakis-whatsapp-btn {
  background: linear-gradient(45deg, #10b981, #059669);
  color: white;
  padding: 0.75rem;
  border-radius: 1rem;
  text-decoration: none;
  display: flex;
  align-items: center;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.kyriakis-whatsapp-btn:hover {
  background: linear-gradient(45deg, #059669, #047857);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
  color: white;
  text-decoration: none;
}

.kyriakis-mobile-menu-btn {
  display: block;
  padding: 0.75rem;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  background: none;
  border: none;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.9);
}

@media (min-width: 1024px) {
  .kyriakis-mobile-menu-btn {
    display: none;
  }
}

.kyriakis-header.scrolled .kyriakis-mobile-menu-btn {
  color: #4b5563;
}

.kyriakis-mobile-menu-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.kyriakis-header.scrolled .kyriakis-mobile-menu-btn:hover {
  background: #f3f4f6;
}

.kyriakis-mobile-menu {
  display: none;
  padding: 1.5rem 0;
  border-top: 1px solid rgba(229, 231, 235, 0.2);
  backdrop-filter: blur(20px);
}

.kyriakis-mobile-menu.active {
  display: block;
}

.kyriakis-mobile-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.kyriakis-mobile-nav-btn {
  text-align: left;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 0.3s ease;
  background: none;
  border: none;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.9);
}

.kyriakis-mobile-nav-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.kyriakis-header.scrolled .kyriakis-mobile-nav-btn {
  color: #374151;
}

.kyriakis-header.scrolled .kyriakis-mobile-nav-btn:hover {
  background: #f3f4f6;
}

.kyriakis-mobile-lang {
  padding: 0.75rem 1rem;
}

.kyriakis-mobile-cta {
  margin-top: 1rem;
  padding: 0 1rem;
}

/* Icons */
.kyriakis-icon {
  width: 1.75rem;
  height: 1.75rem;
  fill: currentColor;
}

.kyriakis-icon-sm {
  width: 1rem;
  height: 1rem;
  fill: currentColor;
}

.kyriakis-icon-md {
  width: 1.25rem;
  height: 1.25rem;
  fill: currentColor;
}
</style>

<header class="kyriakis-header" id="kyriakis-header">
  <div class="kyriakis-header-container">
    <div class="kyriakis-header-content">
      <!-- Logo -->
      <a href="#home" class="kyriakis-logo" onclick="scrollToSection('home')">
        <div class="kyriakis-logo-icon">
          <div class="kyriakis-logo-main">
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/>
            </svg>
          </div>
          <div class="kyriakis-logo-badge">
            <svg class="kyriakis-icon-sm" viewBox="0 0 24 24" fill="currentColor">
              <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
            </svg>
          </div>
        </div>
        <div class="kyriakis-logo-text">
          <h1>Kyriakis Plumber</h1>
          <p id="header-premium">Premium Services 2025</p>
        </div>
      </a>

      <!-- Desktop Navigation -->
      <nav class="kyriakis-nav">
        <button class="kyriakis-nav-btn" onclick="scrollToSection('home')" id="nav-home">Αρχική</button>
        <button class="kyriakis-nav-btn" onclick="scrollToSection('services')" id="nav-services">Υπηρεσίες</button>
        <button class="kyriakis-nav-btn" onclick="scrollToSection('about')" id="nav-about">Σχετικά</button>
        <button class="kyriakis-nav-btn" onclick="scrollToSection('contact')" id="nav-contact">Επικοινωνία</button>
        
        <!-- Language Switcher -->
        <div class="kyriakis-lang-switcher">
          <div class="kyriakis-lang-icon">
            <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <line x1="2" y1="12" x2="22" y2="12"/>
              <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"/>
            </svg>
          </div>
          <div class="kyriakis-lang-buttons">
            <button class="kyriakis-lang-btn active" onclick="switchLanguage('gr')" id="lang-gr">GR</button>
            <button class="kyriakis-lang-btn" onclick="switchLanguage('en')" id="lang-en">EN</button>
          </div>
        </div>

        <!-- CTA Buttons -->
        <div class="kyriakis-cta-buttons">
          <a href="tel:+306985814213" class="kyriakis-call-btn">
            <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
            </svg>
            <span id="header-call">Κλήση</span>
          </a>
          <a href="https://wa.me/306985814213" target="_blank" class="kyriakis-whatsapp-btn" title="WhatsApp">
            <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"/>
            </svg>
          </a>
        </div>
      </nav>

      <!-- Mobile menu button -->
      <button class="kyriakis-mobile-menu-btn" onclick="toggleMobileMenu()">
        <svg class="kyriakis-icon" id="menu-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="3" y1="6" x2="21" y2="6"/>
          <line x1="3" y1="12" x2="21" y2="12"/>
          <line x1="3" y1="18" x2="21" y2="18"/>
        </svg>
        <svg class="kyriakis-icon" id="close-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display: none;">
          <line x1="18" y1="6" x2="6" y2="18"/>
          <line x1="6" y1="6" x2="18" y2="18"/>
        </svg>
      </button>
    </div>

    <!-- Mobile Navigation -->
    <div class="kyriakis-mobile-menu" id="mobile-menu">
      <nav class="kyriakis-mobile-nav">
        <button class="kyriakis-mobile-nav-btn" onclick="scrollToSection('home')" id="mobile-nav-home">Αρχική</button>
        <button class="kyriakis-mobile-nav-btn" onclick="scrollToSection('services')" id="mobile-nav-services">Υπηρεσίες</button>
        <button class="kyriakis-mobile-nav-btn" onclick="scrollToSection('about')" id="mobile-nav-about">Σχετικά</button>
        <button class="kyriakis-mobile-nav-btn" onclick="scrollToSection('contact')" id="mobile-nav-contact">Επικοινωνία</button>
        
        <div class="kyriakis-mobile-lang">
          <div class="kyriakis-lang-switcher">
            <div class="kyriakis-lang-icon">
              <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <line x1="2" y1="12" x2="22" y2="12"/>
                <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"/>
              </svg>
            </div>
            <div class="kyriakis-lang-buttons">
              <button class="kyriakis-lang-btn active" onclick="switchLanguage('gr')" id="mobile-lang-gr">GR</button>
              <button class="kyriakis-lang-btn" onclick="switchLanguage('en')" id="mobile-lang-en">EN</button>
            </div>
          </div>
        </div>
        
        <div class="kyriakis-mobile-cta">
          <a href="tel:+306985814213" class="kyriakis-call-btn" style="width: 100%; justify-content: center;">
            <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
            </svg>
            <span id="mobile-header-call">Κλήση 24/7</span>
          </a>
        </div>
      </nav>
    </div>
  </div>
</header>

<script>
// Language translations
const translations = {
  gr: {
    'nav-home': 'Αρχική',
    'nav-services': 'Υπηρεσίες',
    'nav-about': 'Σχετικά',
    'nav-contact': 'Επικοινωνία',
    'header-call': 'Κλήση',
    'header-premium': 'Premium Services 2025',
    'mobile-nav-home': 'Αρχική',
    'mobile-nav-services': 'Υπηρεσίες',
    'mobile-nav-about': 'Σχετικά',
    'mobile-nav-contact': 'Επικοινωνία',
    'mobile-header-call': 'Κλήση 24/7'
  },
  en: {
    'nav-home': 'Home',
    'nav-services': 'Services',
    'nav-about': 'About',
    'nav-contact': 'Contact',
    'header-call': 'Call',
    'header-premium': 'Premium Services 2025',
    'mobile-nav-home': 'Home',
    'mobile-nav-services': 'Services',
    'mobile-nav-about': 'About',
    'mobile-nav-contact': 'Contact',
    'mobile-header-call': 'Call 24/7'
  }
};

let currentLanguage = 'gr';

// Scroll effect
window.addEventListener('scroll', function() {
  const header = document.getElementById('kyriakis-header');
  if (window.scrollY > 20) {
    header.classList.add('scrolled');
  } else {
    header.classList.remove('scrolled');
  }
});

// Mobile menu toggle
function toggleMobileMenu() {
  const mobileMenu = document.getElementById('mobile-menu');
  const menuIcon = document.getElementById('menu-icon');
  const closeIcon = document.getElementById('close-icon');
  
  if (mobileMenu.classList.contains('active')) {
    mobileMenu.classList.remove('active');
    menuIcon.style.display = 'block';
    closeIcon.style.display = 'none';
  } else {
    mobileMenu.classList.add('active');
    menuIcon.style.display = 'none';
    closeIcon.style.display = 'block';
  }
}

// Language switcher
function switchLanguage(lang) {
  currentLanguage = lang;
  
  // Update active button
  document.getElementById('lang-gr').classList.toggle('active', lang === 'gr');
  document.getElementById('lang-en').classList.toggle('active', lang === 'en');
  document.getElementById('mobile-lang-gr').classList.toggle('active', lang === 'gr');
  document.getElementById('mobile-lang-en').classList.toggle('active', lang === 'en');
  
  // Update text content
  Object.keys(translations[lang]).forEach(key => {
    const element = document.getElementById(key);
    if (element) {
      element.textContent = translations[lang][key];
    }
  });
  
  // Trigger custom event for other blocks
  window.dispatchEvent(new CustomEvent('languageChanged', { detail: { language: lang } }));
}

// Smooth scroll function
function scrollToSection(sectionId) {
  const element = document.getElementById(sectionId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
  
  // Close mobile menu if open
  const mobileMenu = document.getElementById('mobile-menu');
  if (mobileMenu.classList.contains('active')) {
    toggleMobileMenu();
  }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
  switchLanguage('gr');
});
</script>