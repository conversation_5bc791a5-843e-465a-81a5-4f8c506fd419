<?php
/**
 * Title: About Section
 * Slug: kyriakis/about
 * Categories: kyriakis-patterns
 * Description: About section with company information, statistics, and partner logos
 */
?>

<!-- wp:html -->
<section id="about" class="kyriakis-about">
  <div class="kyriakis-about-container">
    <div class="kyriakis-about-content">
      <div class="kyriakis-about-text">
        <div class="kyriakis-about-badge">
          <div class="kyriakis-about-badge-icon">
            <svg class="kyriakis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </div>
          <span class="kyriakis-about-badge-text" id="about-badge">About Our Company</span>
        </div>
        
        <h2 class="kyriakis-about-title" id="about-title">10+ Years of Excellence in Plumbing Services</h2>
        <p class="kyriakis-about-description" id="about-description">
          Since 2014, Kyriakis Plumber has been providing top-quality hydraulic services across Greece. 
          We specialize in modern plumbing solutions, emergency repairs, and complete system installations 
          using only premium materials and advanced techniques.
        </p>
        
        <div class="kyriakis-about-features">
          <div class="kyriakis-about-feature">
            <div class="kyriakis-about-feature-icon">
              <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 12l2 2 4-4"/>
                <circle cx="12" cy="12" r="10"/>
              </svg>
            </div>
            <div class="kyriakis-about-feature-content">
              <h4 id="about-feature-1-title">Licensed & Insured</h4>
              <p id="about-feature-1-description">Fully licensed professional with comprehensive insurance coverage</p>
            </div>
          </div>
          
          <div class="kyriakis-about-feature">
            <div class="kyriakis-about-feature-icon">
              <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
              </svg>
            </div>
            <div class="kyriakis-about-feature-content">
              <h4 id="about-feature-2-title">24/7 Emergency Service</h4>
              <p id="about-feature-2-description">Round-the-clock availability for urgent plumbing emergencies</p>
            </div>
          </div>
          
          <div class="kyriakis-about-feature">
            <div class="kyriakis-about-feature-icon">
              <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <div class="kyriakis-about-feature-content">
              <h4 id="about-feature-3-title">Premium Quality</h4>
              <p id="about-feature-3-description">Only the finest materials and latest techniques for lasting results</p>
            </div>
          </div>
        </div>
        
        <div class="kyriakis-about-cta">
          <a href="tel:+306985814213" class="kyriakis-about-btn">
            <svg class="kyriakis-icon-md" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
            </svg>
            <span id="about-cta-btn">Contact Us Today</span>
          </a>
        </div>
      </div>
      
      <div class="kyriakis-about-stats">
        <div class="kyriakis-about-stats-grid">
          <div class="kyriakis-about-stat">
            <div class="kyriakis-about-stat-number" data-target="500">0</div>
            <div class="kyriakis-about-stat-label" id="about-stat-1">Happy Customers</div>
          </div>
          
          <div class="kyriakis-about-stat">
            <div class="kyriakis-about-stat-number" data-target="10">0</div>
            <div class="kyriakis-about-stat-label" id="about-stat-2">Years Experience</div>
          </div>
          
          <div class="kyriakis-about-stat">
            <div class="kyriakis-about-stat-number" data-target="1000">0</div>
            <div class="kyriakis-about-stat-label" id="about-stat-3">Projects Completed</div>
          </div>
          
          <div class="kyriakis-about-stat">
            <div class="kyriakis-about-stat-number" data-target="24">0</div>
            <div class="kyriakis-about-stat-label" id="about-stat-4">Hour Service</div>
          </div>
        </div>
        
        <!-- Partner Logos -->
        <div class="kyriakis-about-partners">
          <h4 id="about-partners-title">Trusted Partners</h4>
          <div class="kyriakis-about-partners-grid">
            <div class="kyriakis-about-partner">
              <img src="/wp-content/uploads/logo-aquatherm.png" alt="Aquatherm" class="kyriakis-about-partner-logo">
            </div>
            <div class="kyriakis-about-partner">
              <img src="/wp-content/uploads/logo-valsir-en-gb.png" alt="Valsir" class="kyriakis-about-partner-logo">
            </div>
            <div class="kyriakis-about-partner">
              <img src="/wp-content/uploads/uponor.jpg" alt="Uponor" class="kyriakis-about-partner-logo">
            </div>
            <div class="kyriakis-about-partner">
              <img src="/wp-content/uploads/rahau-sunergasia.jpg.webp" alt="Rehau" class="kyriakis-about-partner-logo">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
.kyriakis-about {
  padding: 6rem 0;
  background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
}

.kyriakis-about-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

.kyriakis-about-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 5rem;
  align-items: center;
}

@media (min-width: 1024px) {
  .kyriakis-about-content {
    grid-template-columns: 1fr 1fr;
  }
}

.kyriakis-about-badge {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.kyriakis-about-badge-icon {
  background: linear-gradient(45deg, #2563eb, #9333ea);
  padding: 0.75rem;
  border-radius: 1rem;
  color: white;
}

.kyriakis-about-badge-text {
  color: #2563eb;
  font-weight: 600;
  font-size: 1.125rem;
}

.kyriakis-about-title {
  font-size: 2.5rem;
  font-weight: bold;
  background: linear-gradient(45deg, #111827, #374151);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

@media (min-width: 1024px) {
  .kyriakis-about-title {
    font-size: 3rem;
  }
}

.kyriakis-about-description {
  font-size: 1.125rem;
  color: #4b5563;
  line-height: 1.7;
  margin-bottom: 2.5rem;
}

.kyriakis-about-features {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

.kyriakis-about-feature {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.kyriakis-about-feature-icon {
  background: linear-gradient(45deg, #10b981, #059669);
  color: white;
  padding: 0.75rem;
  border-radius: 1rem;
  flex-shrink: 0;
}

.kyriakis-about-feature-content h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
}

.kyriakis-about-feature-content p {
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

.kyriakis-about-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(45deg, #2563eb, #9333ea);
  color: white;
  padding: 1rem 2rem;
  border-radius: 1rem;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.kyriakis-about-btn:hover {
  background: linear-gradient(45deg, #1d4ed8, #7c3aed);
  transform: translateY(-2px);
  color: white;
  text-decoration: none;
}

.kyriakis-about-stats {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 2rem;
  padding: 3rem 2rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.kyriakis-about-stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

@media (min-width: 768px) {
  .kyriakis-about-stats-grid {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }
}

.kyriakis-about-stat {
  text-align: center;
}

.kyriakis-about-stat-number {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(45deg, #2563eb, #9333ea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.kyriakis-about-stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 600;
}

.kyriakis-about-partners h4 {
  text-align: center;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 2rem;
}

.kyriakis-about-partners-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  align-items: center;
}

@media (min-width: 768px) {
  .kyriakis-about-partners-grid {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }
}

.kyriakis-about-partner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.kyriakis-about-partner:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.kyriakis-about-partner-logo {
  max-width: 80px;
  max-height: 40px;
  object-fit: contain;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.kyriakis-about-partner:hover .kyriakis-about-partner-logo {
  filter: grayscale(0%);
}

/* Icons */
.kyriakis-icon {
  width: 1.75rem;
  height: 1.75rem;
  fill: currentColor;
}

.kyriakis-icon-md {
  width: 1.25rem;
  height: 1.25rem;
  fill: currentColor;
}

/* Animation for counting numbers */
@keyframes countUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.kyriakis-about-stat-number.animate {
  animation: countUp 0.6s ease-out;
}
</style>

<script>
// About section language translations
const aboutTranslations = {
  gr: {
    'about-badge': 'Σχετικά με την Εταιρεία μας',
    'about-title': '10+ Χρόνια Αριστείας στις Υδραυλικές Υπηρεσίες',
    'about-description': 'Από το 2014, ο Kyriakis Plumber παρέχει υψηλής ποιότητας υδραυλικές υπηρεσίες σε όλη την Ελλάδα. Ειδικευόμαστε σε σύγχρονες υδραυλικές λύσεις, επισκευές έκτακτης ανάγκης και πλήρεις εγκαταστάσεις συστημάτων χρησιμοποιώντας μόνο premium υλικά και προηγμένες τεχνικές.',
    'about-feature-1-title': 'Αδειοδοτημένος & Ασφαλισμένος',
    'about-feature-1-description': 'Πλήρως αδειοδοτημένος επαγγελματίας με ολοκληρωμένη ασφαλιστική κάλυψη',
    'about-feature-2-title': '24/7 Υπηρεσία Έκτακτης Ανάγκης',
    'about-feature-2-description': 'Διαθεσιμότητα όλο το εικοσιτετράωρο για επείγουσες υδραυλικές επισκευές',
    'about-feature-3-title': 'Premium Ποιότητα',
    'about-feature-3-description': 'Μόνο τα καλύτερα υλικά και οι πιο σύγχρονες τεχνικές για διαρκή αποτελέσματα',
    'about-cta-btn': 'Επικοινωνήστε Σήμερα',
    'about-stat-1': 'Ικανοποιημένοι Πελάτες',
    'about-stat-2': 'Χρόνια Εμπειρίας',
    'about-stat-3': 'Ολοκληρωμένα Έργα',
    'about-stat-4': 'Ωρών Υπηρεσία',
    'about-partners-title': 'Αξιόπιστοι Συνεργάτες'
  },
  en: {
    'about-badge': 'About Our Company',
    'about-title': '10+ Years of Excellence in Plumbing Services',
    'about-description': 'Since 2014, Kyriakis Plumber has been providing top-quality hydraulic services across Greece. We specialize in modern plumbing solutions, emergency repairs, and complete system installations using only premium materials and advanced techniques.',
    'about-feature-1-title': 'Licensed & Insured',
    'about-feature-1-description': 'Fully licensed professional with comprehensive insurance coverage',
    'about-feature-2-title': '24/7 Emergency Service',
    'about-feature-2-description': 'Round-the-clock availability for urgent plumbing emergencies',
    'about-feature-3-title': 'Premium Quality',
    'about-feature-3-description': 'Only the finest materials and latest techniques for lasting results',
    'about-cta-btn': 'Contact Us Today',
    'about-stat-1': 'Happy Customers',
    'about-stat-2': 'Years Experience',
    'about-stat-3': 'Projects Completed',
    'about-stat-4': 'Hour Service',
    'about-partners-title': 'Trusted Partners'
  }
};

// Counter animation
function animateCounters() {
  const counters = document.querySelectorAll('.kyriakis-about-stat-number');
  
  counters.forEach(counter => {
    const target = parseInt(counter.getAttribute('data-target'));
    const increment = target / 100;
    let current = 0;
    
    const updateCounter = () => {
      if (current < target) {
        current += increment;
        counter.textContent = Math.ceil(current);
        setTimeout(updateCounter, 20);
      } else {
        counter.textContent = target;
      }
    };
    
    // Start animation when element is visible
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          counter.classList.add('animate');
          updateCounter();
          observer.unobserve(entry.target);
        }
      });
    });
    
    observer.observe(counter);
  });
}

// Initialize counters when DOM is loaded
document.addEventListener('DOMContentLoaded', animateCounters);

// Listen for language change events
window.addEventListener('languageChanged', function(e) {
  const lang = e.detail.language;
  Object.keys(aboutTranslations[lang]).forEach(key => {
    const element = document.getElementById(key);
    if (element) {
      element.textContent = aboutTranslations[lang][key];
    }
  });
});
</script>
<!-- /wp:html -->
