import React, { useEffect, useState } from 'react';
import { Phone, Clock, Shield, Star, ArrowRight, <PERSON>rkles, Zap, MessageCircle } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const Hero: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const { t } = useLanguage();

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const scrollToContact = () => {
    const element = document.getElementById('contact');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="relative min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 text-white overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1),transparent_70%)]"></div>
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-20 left-20 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-cyan-500/10 rounded-full blur-3xl animate-pulse delay-500"></div>
        </div>
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-32 pb-20">
        <div className="grid lg:grid-cols-2 gap-16 items-center min-h-[80vh]">
          {/* Content */}
          <div className={`space-y-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <div className="space-y-6">
              <div className="flex items-center gap-3 text-blue-300">
                <div className="bg-gradient-to-r from-blue-500 to-cyan-500 p-2 rounded-xl">
                  <Shield className="w-5 h-5 text-white" />
                </div>
                <span className="text-sm font-semibold tracking-wide uppercase">{t('hero.certified')}</span>
                <Sparkles className="w-4 h-4 text-yellow-400 animate-pulse" />
              </div>
              
              <h1 className="text-5xl lg:text-7xl font-bold leading-tight">
                <span className="bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                  {t('hero.title1')}
                </span>
                <br />
                <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                  {t('hero.title2')}
                </span>
                {t('hero.title3') && (
                  <>
                    <br />
                    <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent">
                      {t('hero.title3')}
                    </span>
                  </>
                )}
                <br />
                <span className="text-2xl lg:text-4xl font-normal text-blue-200 flex items-center gap-3">
                  {t('hero.location')}
                  <Zap className="w-8 h-8 text-yellow-400 animate-bounce" />
                </span>
              </h1>
              
              <p className="text-xl lg:text-2xl text-blue-100 max-w-2xl leading-relaxed">
                {t('hero.description').split('εγγύηση ποιότητας').map((part, index, array) => (
                  <React.Fragment key={index}>
                    {part}
                    {index < array.length - 1 && (
                      <span className="text-yellow-400 font-semibold">{t('hero.quality')}</span>
                    )}
                  </React.Fragment>
                ))}
              </p>
            </div>

            {/* Features */}
            <div className="grid sm:grid-cols-2 gap-4">
              {[
                { icon: Clock, text: t('hero.emergency'), color: 'from-green-500 to-emerald-500' },
                { icon: Star, text: t('hero.experience'), color: 'from-yellow-500 to-orange-500' },
                { icon: Shield, text: t('hero.certified'), color: 'from-blue-500 to-cyan-500' },
                { icon: Sparkles, text: t('hero.technology'), color: 'from-purple-500 to-pink-500' }
              ].map((feature, index) => (
                <div key={index} className="group">
                  <div className="flex items-center gap-4 bg-white/5 backdrop-blur-xl rounded-2xl p-4 border border-white/10 hover:bg-white/10 transition-all duration-300 hover:scale-105">
                    <div className={`bg-gradient-to-r ${feature.color} p-3 rounded-xl shadow-lg group-hover:shadow-xl transition-all duration-300`}>
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                    <span className="font-semibold text-lg">{feature.text}</span>
                  </div>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col gap-6 pt-4">
              <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={scrollToContact}
                className="group bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-5 rounded-2xl font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-blue-500/25 flex items-center justify-center gap-3"
              >
                {t('hero.quote')}
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
              </button>
              <a
                href="tel:+306985814213"
                className="group bg-white/10 backdrop-blur-xl hover:bg-white/20 text-white px-10 py-5 rounded-2xl font-bold text-lg transition-all duration-300 border border-white/20 hover:border-white/40 flex items-center justify-center gap-3 hover:scale-105"
              >
                <Phone className="w-5 h-5 group-hover:animate-pulse" />
                {t('hero.call')}
              </a>
              </div>
              
              {/* WhatsApp and Viber Buttons */}
              <div className="flex gap-4 justify-center">
                <a
                  href="https://wa.me/306985814213"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="group bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-8 py-4 rounded-2xl font-bold transition-all duration-300 flex items-center gap-3 hover:scale-105 shadow-xl"
                >
                  <MessageCircle className="w-5 h-5 group-hover:animate-bounce" />
                  WhatsApp
                </a>
              </div>
            </div>
          </div>

          {/* Visual Dashboard */}
          <div className={`relative transition-all duration-1000 delay-300 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
            <div className="relative">
              {/* Main Dashboard */}
              <div className="bg-white/10 backdrop-blur-2xl rounded-3xl p-8 shadow-2xl border border-white/20">
                <div className="grid grid-cols-2 gap-6">
                  {[
                    { number: '1000+', label: t('about.clients'), color: 'from-blue-500 to-cyan-500', icon: '👥' },
                    { number: '24/7', label: 'Διαθεσιμότητα', color: 'from-green-500 to-emerald-500', icon: '🕒' },
                    { number: '30+', label: t('about.years'), color: 'from-yellow-500 to-orange-500', icon: '⭐' },
                    { number: '100%', label: 'Εγγύηση', color: 'from-purple-500 to-pink-500', icon: '🛡️' }
                  ].map((stat, index) => (
                    <div key={index} className="group">
                      <div className={`bg-gradient-to-br ${stat.color} rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 border border-white/20`}>
                        <div className="text-2xl mb-2">{stat.icon}</div>
                        <div className="text-3xl font-bold text-white mb-1">{stat.number}</div>
                        <div className="text-sm text-white/90 font-medium">{stat.label}</div>
                      </div>
                    </div>
                  ))}
                </div>
                
                {/* Live Status Indicator */}
                <div className="mt-6 flex items-center justify-center gap-3 bg-green-500/20 rounded-2xl p-4 border border-green-500/30">
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-300 font-semibold">{t('hero.available')}</span>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl p-4 shadow-xl animate-bounce">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <div className="absolute -bottom-4 -left-4 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-2xl p-4 shadow-xl animate-pulse">
                <Zap className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default Hero;